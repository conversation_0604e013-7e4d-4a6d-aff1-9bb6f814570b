{"nm": "Comp 1", "ddd": 0, "h": 1500, "w": 1500, "meta": {"g": "@lottiefiles/toolkit-js 0.33.2"}, "layers": [{"ty": 3, "nm": "NULL CONTROL", "sr": 1, "st": 0, "op": 1800, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [60, 60, 0], "ix": 1}, "s": {"a": 0, "k": [165, 165, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [750, 750, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0, "ix": 11}}, "ef": [], "ind": 1}, {"ty": 4, "nm": "Path 1", "sr": 1, "st": 2, "op": 1802, "ip": 2, "hd": false, "cl": "error-symbol", "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [157.03, 157.03, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 100], "t": 1.375}, {"s": [100, 100, 100], "t": 11}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [60, 60, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Overshoot", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Bounce", "ix": 2, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Friction", "ix": 3, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Path 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[157.03, 104.687], [157.03, 170.116]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 3", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[157.03, 222.459], [157.17, 222.459], [157.17, 222.599], [157.03, 222.599]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 18.5, "ix": 5}, "c": {"a": 0, "k": [0.4863, 0.8275, 0.9725], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 2, "parent": 1}, {"ty": 4, "nm": "Path 2", "sr": 1, "st": 0, "op": 1800, "ip": 0, "hd": false, "cl": "error-border", "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [157.03, 157.03, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 100], "t": 0}, {"s": [100, 100, 100], "t": 9.625}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [60, 60, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Overshoot", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Bounce", "ix": 2, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Friction", "ix": 3, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Path 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, -86.725], [86.725, 0], [0, 86.725], [-86.725, 0]], "o": [[0, 86.725], [-86.725, 0], [0, -86.725], [86.725, 0]], "v": [[314.06, 157.03], [157.03, 314.06], [0, 157.03], [157.03, 0]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 3}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [27.5], "t": 12}, {"s": [18.5], "t": 24}], "ix": 5}, "c": {"a": 0, "k": [0.4863, 0.8275, 0.9725], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.24, "y": 1}, "s": [0], "t": 0}, {"s": [100], "t": 14}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 1}], "ind": 3, "parent": 1}], "v": "5.9.6", "fr": 30, "op": 63, "ip": 0, "assets": []}