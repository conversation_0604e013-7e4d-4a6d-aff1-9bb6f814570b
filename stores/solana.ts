import { Buff<PERSON> } from "buffer";
globalThis.Buffer = globalThis.Buffer || Buffer

import { useAppKitConnection, type Connection, type Provider } from "@reown/appkit-adapter-solana/vue";
import { useAppKitAccount, useAppKitNetwork, useAppKitProvider, type UseAppKitAccountReturn, type UseAppKitNetworkReturn } from "@reown/appkit/vue";
import type { Web3Kit } from "~/types/web3Kit";
import { LAMPORTS_PER_SOL, PublicKey } from "@solana/web3.js";
import bs58 from 'bs58';
import { StepVestingProgram } from "@secondswap/sdk"

export class SolanaKit implements Web3Kit {
	constructor() {}

	async signMessage(message: string) {
		try {
			const { walletProvider: proxiedWalletProvider } = useAppKitProvider<Provider>("solana")
			if (!proxiedWalletProvider) throw new Error("Wallet provider not found")
			const walletProvider = toRaw(proxiedWalletProvider)
			console.log("signMessage Wallet Provider", walletProvider)
      // 2. Encode message and sign it
      const encodedMessage = new TextEncoder().encode(message);
      const signature = await walletProvider.signMessage(encodedMessage);
			// Convert signature to base58 string
			const signatureString = bs58.encode(signature);

			return {
				signature: signatureString,
			};
    } catch (err) {
			console.error(`error signing: ${err}`);
			throw err;
    }
	}

	async getNativeBalance(address: string): Promise<string> {
		const { connection } = useAppKitConnection()
		if (!connection) throw new Error("Connection not found")
		const wallet = new PublicKey(address);
		const balance = await connection.getBalance(wallet);
		return String(balance ? balance / LAMPORTS_PER_SOL : 0);
	}

	async claimToken(tokenAddress: string) {
		const { walletProvider: proxiedWalletProvider } = useAppKitProvider<Provider>("solana")
		if (!proxiedWalletProvider) throw new Error("Wallet provider not found")
		const walletProvider = toRaw(proxiedWalletProvider)
		const { connection } = useAppKitConnection()
		if (!connection) throw new Error("Connection not found")
		const vestingPlanPub = new PublicKey(tokenAddress)
		const userPublicKey = walletProvider.publicKey
		if (!userPublicKey) throw new Error("User public key not found")
		try {
			const stepVestingProgram = new StepVestingProgram(connection)
			stepVestingProgram.idl = Object.assign({}, stepVestingProgram.idl, {
				address: 'AQWVBiXV1zVFMRHYVMBQRDkTfWQCEkBZrfat1z3A1YEF',
			});
			const vestingSchedulePub = await stepVestingProgram.getVestingSchedulePublicKey(vestingPlanPub, userPublicKey)
			console.log('vestingSchedulePub', vestingSchedulePub)
			const claimTxResult = await stepVestingProgram.claimVestingScheduleTxn(userPublicKey, vestingPlanPub, vestingSchedulePub)
			const { blockhash } = await connection.getLatestBlockhash('finalized')
			claimTxResult.recentBlockhash = blockhash
			claimTxResult.feePayer = userPublicKey
			console.log('claimTxResult', claimTxResult)
			await walletProvider.sendTransaction(claimTxResult, connection)
			if (!claimTxResult) throw new Error("Claim transaction failed")
			return true
		} catch (err) {
			console.error(`error claiming token: ${err}`)
			throw err
		}
	}
}
