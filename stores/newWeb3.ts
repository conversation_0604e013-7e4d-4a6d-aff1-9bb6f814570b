import { Buffer } from "buffer";
globalThis.Buffer = globalThis.Buffer || Buffer
import { defineStore } from 'pinia';

import { createAppKit, useAppKitAccount, useAppKitEvents, useAppKitNetwork, useAppKitProvider, useDisconnect } from '@reown/appkit/vue'
import { mainnet, avalanche, sepolia, solana, solanaTestnet, solanaDevnet, type AppKitNetwork, zksync } from '@reown/appkit/networks'
import { EthersAdapter } from '@reown/appkit-adapter-ethers'
import { SolanaAdapter, useAppKitConnection, type Provider } from "@reown/appkit-adapter-solana/vue";
import type { Web3Kit } from '~/types/web3Kit';
import { EthersKit } from './ethers';
import type { Eip1193Provider } from 'ethers';

const ethersAdapter = new EthersAdapter()
const solanaWeb3JsAdapter = new SolanaAdapter();

export const useNewWeb3Store = defineStore('newWeb3', () => {
	const networks: [AppKitNetwork, ...AppKitNetwork[]] = useRuntimeConfig().public.web3Mode == 'main'
		? [mainnet, avalanche, solana] as const
		: [sepolia, avalanche, solanaDevnet] as const

	const networksByChainId = {
		[mainnet.id]: mainnet,
		[sepolia.id]: sepolia,
		[avalanche.id]: avalanche,
		[solana.id]: solana,
		[solanaTestnet.id]: solanaTestnet,
		[solanaDevnet.id]: solanaDevnet,
		[zksync.id]: zksync,
	}

	const networksBySymbol = useRuntimeConfig().public.web3Mode == 'main' ? {
		[mainnet.nativeCurrency.symbol]: mainnet,
		[avalanche.nativeCurrency.symbol]: avalanche,
		[solana.nativeCurrency.symbol]: solana,
	} : {
		[sepolia.nativeCurrency.symbol]: sepolia,
		[avalanche.nativeCurrency.symbol]: avalanche,
		[solanaTestnet.nativeCurrency.symbol]: solanaTestnet,
	}
	
	const isLoggedIn = ref<boolean>(false)
	const account = useAppKitAccount()
	const events = useAppKitEvents();
	const networkData = useAppKitNetwork();
	const web3Kit = ref<Web3Kit | null>(null)
	const accountNativeBalance = ref<string | null>(null)

	onMounted(() => {
		setIsLoggedIn(window.localStorage.getItem("isLoggedIn") == "true")
	})

	watch(() => account.value.address, async () => {
		if (account.value.address) {
			await setupWeb3Kit()
		}
	})

	watch(events, async () => {
		switch (events.data.event) {
			case "CONNECT_SUCCESS":
				setupWeb3Kit();
				break;

			case "SWITCH_NETWORK":
				setIsLoggedIn(false)
				setupWeb3Kit();
				break;

			case "DISCONNECT_SUCCESS":
				web3Kit.value = null
				window.localStorage.clear()
				setIsLoggedIn(false)
				break;
		}
	});

	async function getAccountNativeBalance(address: string) {
		accountNativeBalance.value = await web3Kit.value?.getNativeBalance(address) || null
		return accountNativeBalance.value
	}

	const modal = createAppKit({
		// 1. Get projectId from https://cloud.reown.com
		projectId: useRuntimeConfig().public.reownProjectId,
		themeMode: "dark",
		themeVariables: {
				'--w3m-accent': '#7CD3F8',
		},
		adapters: [ethersAdapter, solanaWeb3JsAdapter],
		networks: networks,
		metadata: {
			name: "SecondSwap",
			description: "",
			url: "https://secondswap.io", // url must match your domain & subdomain
			icons: ["https://avatars.mywebsite.com/"],
		},
		features: {
				swaps: false,
				onramp: false,
				email: false,
				socials: false,
				analytics: false // Optional - defaults to your Cloud configuration
		},
		includeWalletIds: [
			"c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96",
			"4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0",
			"20459438007b75f4f4acb98bf29aa3b800550309646d375da5fd4aac6c2a2c66",
			"fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa",
			"0b415a746fb9ee99cce155c2ceca0c6f6061b1dbca2d722b3ba16381d0562150",
			"f323633c1f67055a45aac84e321af6ffe46322da677ffdd32f9bc1e33bafe29c",
			"a797aa35c0fadbfc1a53e7f675162ed5226968b44a19ee3d24385c64d1d3c393",
			"1ca0bdd4747578705b1939af023d120677c64fe6ca76add81fda36e350605e79",
		],
	})

	function connectWallet() {
		return modal.open()
	}

	async function disconnect() {
		web3Kit.value = null
		useDisconnect().disconnect()
		window.localStorage.clear()
		setIsLoggedIn(false)
		modal.close()

		await logout();

		window.location.href = '/'
	}

	function setupWeb3Kit() {
		if (!account.value.address) return
		try {
			if ([solana.id, solanaTestnet.id, solanaDevnet.id].find(item => item === networkData.value.chainId)) {
				web3Kit.value = new SolanaKit()
			} else {
				web3Kit.value = new EthersKit()
			}
			getAccountNativeBalance(account.value.address || "")
		} catch (error) {
			console.error('setupWeb3Kit error', error);
		}
	}

	function setIsLoggedIn(value: boolean) {
		isLoggedIn.value = value
		window.localStorage.setItem("isLoggedIn", value.toString())
	}

	async function logout() {
		try {
				await api.apiCall('POST', '/user/logout');
		} catch (error) {
				console.error('logout api res', error);
		}
	}

	return {
		isLoggedIn,
		accountNativeBalance,
		getAccountNativeBalance,
		setIsLoggedIn,
		connectWallet,
		disconnect,
		web3Kit,
		setupWeb3Kit,
		networksByChainId,
		networksBySymbol,
	}
});
