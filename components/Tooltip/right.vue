<template>
  <div class="relative inline-block group">
    <div class="">
      <inline-svg :src="infoIcon" class="stroke-input-icons min-w-3 min-h-3"></inline-svg>
    </div>
    <div class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition 
      bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] left-full top-1/2 -translate-y-1/2 ml-2 shadow-lg
      ">
      <div class="max-h-[100px] p-3 overflow-y-auto w-full">
        <div v-html="content"></div>
      </div>
      
      
      <!-- Triangle pointer -->
      <div class="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-modal-textbox border-l border-b border-textbox-border rotate-45"></div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import infoIcon from "~/assets/images_new/icons/info.svg";

const props = defineProps({
  content: String,
});


</script>

<style>

</style>