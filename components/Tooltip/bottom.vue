<template>
  <div class="relative inline-block group">
    <div>
      <inline-svg :src="infoIcon" class="stroke-input-icons"></inline-svg>
    </div>

    <div class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-gray-900 
    text-white text-sm rounded-lg py-2 px-3 w-[200px] top-full left-1/2 -translate-x-1/2 mt-2 shadow-lg">
    <p>hellooooo</p>

      <!-- Triangle pointer -->
      <div class="absolute -top-1 left-1/2 -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import infoIcon from "~/assets/images_new/icons/info.svg";

const props = defineProps({
  content: Object,
});

</script>

<style>

</style>