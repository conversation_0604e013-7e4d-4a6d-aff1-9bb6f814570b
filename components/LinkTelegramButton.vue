<template>
	<button type="button" @click="linkToTelegram">
		<slot />
	</button>
	<ModalConnect ref="connectModal"></ModalConnect>
</template>

<script setup>
import { useAppKitAccount, useAppKitNetwork } from "@reown/appkit/vue";
import api from "../utils/api";
import dayjs from "dayjs";

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn, web3Kit } = storeToRefs(newWeb3Store);
const { networksByChainId } = newWeb3Store;
const account = useAppKitAccount();
const networkData = useAppKitNetwork()

const emit = defineEmits(["onSuccess"])

const connectModal = ref(null);

const showLoginModal = () => {
	connectModal.value.handleClick();
	document.getElementById("connectModal").checked = true;
}

const linkToTelegram = async () => {
	if (!isLoggedIn.value) {
		showLoginModal()
		return
	}

	useSweetAlertStore().showLoadingAlert("Processing")

	try {
		const timestamp = dayjs().valueOf()

		console.log("web3Kit.value", web3Kit.value)
		const signMessageResponse = await web3Kit.value?.signMessage(
			`Connect with SecondSwap Telegram bot\n` +
			`Address: ${account.value.address}\n` +
			`Nonce: ${timestamp}`
		)

		const linkTelegramResponse = await api.apiCall("POST", "/telegram/link", {
			network: networksByChainId[networkData.value.chainId]?.nativeCurrency.symbol,
			nonce: Number(timestamp),
			signature: signMessageResponse.signature,
		})

		window.open(linkTelegramResponse.data.message.url, "_blank")

		useSweetAlertStore().showAlert("Success", "Link to Telegram successfully", "success");
		emit("onSuccess")
	} catch (error) {
		console.error(error)
		if (error.status === 400) {
			useSweetAlertStore().showAlert("Error", "Telegram account is already linked", "error");
		} else {
			useSweetAlertStore().showAlert("Error", "Failed to link Telegram account", "error");
		}
	}
}


</script>

<style scoped></style>
