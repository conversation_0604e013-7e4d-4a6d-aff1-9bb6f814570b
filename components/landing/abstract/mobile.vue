<template>
  <div class="pin-container block lg:hidden" ref="containerRef">
    <div class="pin-section">
      <div class="sticky top-0 h-screen flex items-center justify-center">
        <div class="relative w-full">
          <img
            class="w-[270px] h-[261px] m-auto transition-all duration-700"
            :style="{
              transform: `rotate(${rotationAngle}deg)`,
              opacity: imageOpacity,
            }"
            :src="section2Image"
            alt="section2"
          />
          <div
            class="bg-section2-image w-[331px] h-[331px] blur-[72px] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
          ></div>

          <div class="section2-feature-item relative">
            <img
              class="w-[10px] h-[64px] mx-auto mb-5"
              :src="abstractPointImg"
              alt="abstract point"
            />

            <!-- Sections container with fixed position -->
            <div class="section2-content-container">
              <!-- For Buyers section -->
              <div
                class="section2-content text-center transition-all duration-700"
                :class="{
                  'opacity-0': !sectionsVisible.buyers,
                  'opacity-100': sectionsVisible.buyers,
                }"
              >
                <h3 class="text-xl font-black text-white mb-2 tracking-[0.4px]">
                  For Buyers
                </h3>
                <h6 class="text-2sm text-white/50 tracking-[0.02em]">
                  Get discounts on high demand tokens
                </h6>
              </div>

              <!-- For Sellers section -->
              <div
                class="section2-content text-center transition-all duration-700 absolute top-0 left-0 w-full"
                :class="{
                  'opacity-0': !sectionsVisible.sellers,
                  'opacity-100': sectionsVisible.sellers,
                }"
              >
                <h3 class="text-xl font-black text-white mb-2 tracking-[0.4px]">
                  For Sellers
                </h3>
                <h6 class="text-2sm text-white/50 tracking-[0.02em]">
                  Realize profits by listing their locked allocation
                </h6>
              </div>

              <!-- For Token Issuers section -->
              <div
                class="section2-content text-center transition-all duration-700 absolute top-0 left-0 w-full"
                :class="{
                  'opacity-0': !sectionsVisible.tokenIssuers,
                  'opacity-100': sectionsVisible.tokenIssuers,
                }"
              >
                <h3 class="text-xl font-black text-white mb-2 tracking-[0.4px]">
                  For Token Issuers
                </h3>
                <h6 class="text-2sm text-white/50 tracking-[0.02em]">
                  Unlock liquidity without impact to circulating supply
                </h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import inlineSvg from "vue-inline-svg";
import section2Image from "~/assets/images_new/landing/abstract_3x.webp";
import abstractPointImg from "~/assets/images_new/landing/abstract_point_4x.webp";
import { ref, reactive, onMounted, onUnmounted } from "vue";

const containerRef = ref(null);
const sectionsVisible = reactive({
  buyers: true,
  sellers: false,
  tokenIssuers: false,
});

const rotationAngle = ref(0);
const imageOpacity = ref(1);

onMounted(() => {
  const handleScroll = () => {
    if (!containerRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    const containerHeight = containerRect.height;
    const scrollProgress =
      -containerRect.top / (containerHeight - window.innerHeight);

    // Reset all to false first
    sectionsVisible.buyers = false;
    sectionsVisible.sellers = false;
    sectionsVisible.tokenIssuers = false;

    // Temporarily reduce opacity during transitions
    imageOpacity.value = 0.5;

    // Set rotation and visibility based on scroll position
    if (scrollProgress < 0.33) {
      rotationAngle.value = 0;
      sectionsVisible.buyers = true;
    } else if (scrollProgress < 0.66) {
      rotationAngle.value = 20;
      sectionsVisible.sellers = true;
    } else {
      rotationAngle.value = 40;
      sectionsVisible.tokenIssuers = true;
    }

    // Restore full opacity after a short delay
    setTimeout(() => {
      imageOpacity.value = 1;
    }, 100);
  };

  window.addEventListener("scroll", handleScroll);
  handleScroll(); // Initial check

  onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
  });
});
</script>

<style scoped>
.bg-section2-image {
  flex-shrink: 0;
  border-radius: 331px;
  opacity: 0.4;
  background: var(
    --GD,
    linear-gradient(90deg, #51bef7 0%, #8c96f2 36.5%, #bd63eb 66%, #df5b9a 100%)
  );
}

.pin-container {
  height: 300vh;
  position: relative;
}

.pin-section {
  height: 100vh;
  position: sticky;
  top: 0;
}

.section2-content-container {
  position: relative;
  height: 100px; /* Adjust based on your content height */
}
</style>
