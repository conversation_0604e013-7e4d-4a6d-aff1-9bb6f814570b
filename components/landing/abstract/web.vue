<template>
  <div class="pin-container hidden lg:block" ref="containerRef">
    <div class="pin-section">
      <div class="sticky top-1/2 -translate-y-1/2">
        <div class="relative">
          <div class="w-fit mx-auto relative">
            <img
              class="w-[469px] h-[439px] mx-auto"
              :src="abstractImg"
              alt="section2"
            />
            <div
              class="bg-section2-image w-[780px] h-[780px] blur-[187px] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            ></div>

            <div
              class="absolute left-[400px] top-[84px] flex gap-5 w-[486px] h-[100px] transition-all duration-700"
              ref="forBuyersSection"
              :class="{
                'opacity-0 -translate-x-12': !sectionsVisible.buyers,
                'opacity-100 translate-x-0': sectionsVisible.buyers,
              }"
            >
              <div class="w-[135px] h-[42px]">
                <img :src="forBuyersPointer" alt="pointer" />
              </div>
              <div class="relative bottom-5">
                <h2
                  class="text-4xl font-black text-white whitespace-nowrap mb-4"
                >
                  For Buyers
                </h2>
                <p
                  class="text-white/50 font-normal text-base tracking-[0.32px]"
                >
                  Get discounts on high demand <br />
                  tokens
                </p>
              </div>
            </div>

            <div
              class="absolute right-[424px] top-[248px] flex gap-5 justify-end w-[486px] h-[100px] transition-all duration-700"
              ref="forSellersSection"
              :class="{
                'opacity-0 -translate-x-12': !sectionsVisible.sellers,
                'opacity-100 translate-x-0': sectionsVisible.sellers,
              }"
            >
              <div class="relative top-2 text-right">
                <h2
                  class="text-4xl font-black text-white whitespace-nowrap mb-4"
                >
                  For Sellers
                </h2>
                <p
                  class="text-white/50 font-normal text-base tracking-[0.32px]"
                >
                  Realize profits by listing their locked allocation
                </p>
              </div>
              <div class="w-[135px] h-[42px]">
                <img :src="forSellersPointer" alt="pointer" />
              </div>
            </div>

            <div
              class="absolute left-[400px] top-[338px] flex gap-5 w-[486px] h-[100px] transition-all duration-700"
              ref="forTokenIssuersSection"
              :class="{
                'opacity-0 -translate-x-12': !sectionsVisible.tokenIssuers,
                'opacity-100 translate-x-0': sectionsVisible.tokenIssuers,
              }"
            >
              <div class="w-[135px] h-[42px]">
                <img :src="forTokenIssuersPointer" alt="pointer" />
              </div>
              <div class="relative top-3">
                <h2
                  class="text-4xl font-black text-white whitespace-nowrap mb-4"
                >
                  For Token Issuers
                </h2>
                <p
                  class="text-white/50 font-normal text-base tracking-[0.32px]"
                >
                  Unlock liquidity without impact <br />
                  to circulating supply
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import abstractImg from "~/assets/images_new/landing/abstract_3x.webp";
import forBuyersPointer from "~/assets/images_new/landing/for-buyers-pointer_4x.webp";
import forSellersPointer from "~/assets/images_new/landing/for-sellers-pointer_4x.webp";
import forTokenIssuersPointer from "~/assets/images_new/landing/for-token-issuers-pointer_4x.webp";
import { onMounted, ref, reactive, onUnmounted } from "vue";

const containerRef = ref(null);
const forBuyersSection = ref(null);
const forSellersSection = ref(null);
const forTokenIssuersSection = ref(null);

const sectionsVisible = reactive({
  buyers: false,
  sellers: false,
  tokenIssuers: false,
});

onMounted(() => {
  const handleScroll = () => {
    if (!containerRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    const containerHeight = containerRect.height;
    const scrollProgress =
      -containerRect.top / (containerHeight - window.innerHeight);

    // Trigger sections at different scroll positions
    sectionsVisible.buyers = scrollProgress > 0.2;
    sectionsVisible.sellers = scrollProgress > 0.4;
    sectionsVisible.tokenIssuers = scrollProgress > 0.6;
  };

  window.addEventListener("scroll", handleScroll);
  // Initial check
  handleScroll();

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
  });
});
</script>

<style scoped>
.pin-container {
  /* Increased height to give more room for scroll triggers */
  height: 400vh;
  position: relative;
}

.pin-section {
  height: 100vh;
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-section2-image {
  flex-shrink: 0;
  border-radius: 331px;
  opacity: 0.4;
  background: var(
    --GD,
    linear-gradient(
      270deg,
      #51bef7 0%,
      #8c96f2 36.5%,
      #bd63eb 66%,
      #df5b9a 100%
    )
  );
}
</style>
