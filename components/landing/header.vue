<template>
  <!-- header -->
  <div
    class="p-4 lg:px-4 max-w-[1382px] mx-auto flex justify-between items-center bg-[#060A22] mb-[70px] lg:mb-0"
  >
    <div>
      <img
        class="block lg:hidden w-[24px] h-[30px"
        :src="secondswapLogo"
        alt="SecondSwap"
      />
      <img
        class="hidden lg:block w-[170px] h-[33px]"
        :src="secondswapFullLogo"
        alt="SecondSwap"
      />
    </div>
    <div class="flex gap-3">
      <!-- <div
        class="bg-primary/20 rounded-full h-[40px] w-[40px] flex items-center justify-center"
      >
        <inline-svg :src="searchIcon"></inline-svg>
      </div> -->

      <div>
        <nuxt-link to="/" target="_blank" class="btn btn-primary !rounded-full h-10 min-h-10 lg:w-[170px]">
          Launch App
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import secondswapLogo from "~/assets/images_new/landing/2s-logo_4x.webp";
import secondswapFullLogo from "~/assets/images_new/landing/2s-full-logo_4x.webp";

import searchIcon from "~/assets/images_new/landing/icons/search.svg";
import inlineSvg from "vue-inline-svg";
</script>

<style scoped></style>
