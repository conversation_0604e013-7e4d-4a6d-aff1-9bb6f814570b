<template>
  <div class="px-5 py-8 border-t border-white/10">
    <div class="relative flex justify-center mb-8">
      <div class="absolute top-0 left-0 flex items-center h-full text-gray-500 text-2sm">v{{ version }}</div>
      <img
        class="w-[120px] h-[24px]"
        :src="secondswapFullLogo"
        alt="secondswap-full-logo"
      />
    </div>

    <div
      class="flex flex-col-reverse items-center justify-between gap-8 lg:flex-row"
    >
      <div class="flex items-center gap-4 text-white">
        <h5 class="text-sm">Audited by :</h5>
        <!-- <img
          class="w-[81.684px] h-[16px]"
          :src="code4renaLogo"
          alt="code4rena-logo"
        /> -->
        <img
          class="w-[50.591px] h-[14px]"
          :src="zellicLogo"
          alt="zellic-logo"
        />
      </div>

      <div class="flex gap-6 text-sm text-white lg:mr-0">
        <a
          href="https://docs.secondswap.io/misc/legal-disclaimer/privacy-policy"
          target="_blank"
          >Privacy Policy</a
        >
        <a
          href="https://docs.secondswap.io/misc/legal-disclaimer/terms-of-use"
          target="_blank"
          >Terms of Use</a
        >
        <a
          class="flex items-center gap-1"
          href="https://docs.secondswap.io/"
          target="_blank"
        >
          <h5>Docs</h5>
          <inline-svg width="16" height="16" :src="arrowRightIcon"></inline-svg>
        </a>
      </div>

      <div class="flex items-center gap-4 text-sm text-white">
        <a
          :href="useRuntimeConfig().public.feedbackFormUrl"
          target="_blank"
          class="text-primary"
        >
          Feedback
        </a>
        <div
          class="w-[30px] h-[30px] rounded-full bg-white/10 flex items-center justify-center"
        >
          <a href="https://medium.com/secondswap-io" target="_blank">
            <inline-svg
              class="fill-primary"
              width="16"
              height="16"
              :src="mediumIcon"
            ></inline-svg>
          </a>
        </div>
        <div
          class="w-[30px] h-[30px] rounded-full bg-white/10 flex items-center justify-center"
        >
          <a href="https://www.linkedin.com/company/secondswap" target="_blank">
            <inline-svg
              class="fill-primary"
              width="16"
              height="16"
              :src="linkedinIcon"
            ></inline-svg>
          </a>
        </div>
        <div
          class="w-[30px] h-[30px] rounded-full bg-white/10 flex items-center justify-center"
        >
          <a href="https://x.com/secondswap_io" target="_blank">
            <inline-svg
              class="fill-primary"
              width="16"
              height="16"
              :src="twitterIcon"
            ></inline-svg>
          </a>
        </div>

        <div
          class="w-[30px] h-[30px] rounded-full bg-white/10 flex items-center justify-center"
        >
          <a href="https://t.me/secondswap_community" target="_blank">
            <inline-svg
              class="fill-primary"
              width="16"
              height="16"
              :src="telegramIcon"
            ></inline-svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { version } from '../package.json'

import inlineSvg from "vue-inline-svg";
import arrowRightIcon from "~/assets/images_new/landing/icons/arrow-right-up.svg";
import secondswapFullLogo from "~/assets/images_new/landing/2s-full-logo_4x.webp";

import code4renaLogo from "~/assets/images_new/landing/auditors/code4rena.webp";
import zellicLogo from "~/assets/images_new/landing/auditors/zellic.webp";

import telegramIcon from "~/assets/images_new/icons/social_media/telegram.svg";
import mediumIcon from "~/assets/images_new/icons/social_media/medium.svg";
import twitterIcon from "~/assets/images_new/icons/social_media/twitter.svg";
import linkedinIcon from "~/assets/images_new/icons/social_media/linkedin.svg";
</script>

<style lang="scss" scoped></style>
