<template>
  <div class="px-0 mb-[80px] lg:mb-[220px] overflow-hidden">
    <h3
      class="text-2xl lg:text-4xl font-black lg:font-normal mb-10 text-center text-white lg:text-white/50"
    >
      We Are Backed & Trusted By
    </h3>

    <div>
      <!-- Mobile horizontal scroll -->
      <div class="block lg:hidden relative w-full overflow-hidden">
        <div class="flex gap-4 animate-[scroll_10s_linear_infinite] whitespace-nowrap">
          <img
            :src="trustMobileImg"
            alt="trust"
            class="w-[1529px] max-w-[1529px] h-auto"
          />
          <img
            :src="trustMobileImg"
            alt="trust"
            class="w-[1529px] max-w-[1529px] h-auto"
          />
          <img
            :src="trustMobileImg"
            alt="trust"
            class="w-[1529px] max-w-[1529px] h-auto"
          />
          <img
            :src="trustMobileImg"
            alt="trust"
            class="w-[1529px] max-w-[1529px] h-auto"
          />
        </div>
      </div>

      <!-- Desktop vertical scroll -->
      <div
        class="hidden lg:block h-[258px] max-h-[258px] overflow-hidden relative"
      >
        <div
          class="flex flex-col gap-2 animate-[scrollVertical_20s_linear_infinite] overflow-hidden"
        >
          <img
            :src="trustImg"
            alt="trust"
            class="w-[1440px] max-w-[1440px] h-[258px] mx-auto"
          />
          <img
            :src="trustImg"
            alt="trust"
            class="w-[1440px] max-w-[1440px] h-[258px] mx-auto"
          />
        </div>

        <div
          class="absolute bg-base-100 -top-[60px] left-1/2 -translate-x-1/2 w-[200%] h-[240px] lg:h-[84px] overflow-hidden"
          style="
            filter: blur(30px);
            -webkit-filter: blur(30px);
            transform: translate(-50%) scale(1.2);
            -webkit-transform: translate(-50%) scale(1.2);
          "
        ></div>

        <div
          class="absolute bg-base-100 -bottom-[50px] left-1/2 -translate-x-1/2 w-[200%] h-[240px] lg:h-[84px]"
          style="
            filter: blur(30px);
            -webkit-filter: blur(30px);
            transform: translate(-50%) scale(1.2);
            -webkit-transform: translate(-50%) scale(1.2);
          "
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import trustImg from "~/assets/images_new/landing/trusted_by/trusted-by_3x.webp";
import trustMobileImg from "~/assets/images_new/landing/trusted_by/trusted-by-mobile_3x.webp";

const SwiperAutoplay = Autoplay;
</script>

<style>

@keyframes scrollVertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-50%);
  }
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-1545px);
  }
}
</style>
