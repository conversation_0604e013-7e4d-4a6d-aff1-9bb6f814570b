<template>
  <div class="justify-center gap-5 mx-auto mb-[188px] hidden lg:flex">
    <a
      v-for="(news, index) in recentNews"
      :href="news.link"
      target="_blank"
      :class="`w-[288px] h-[335px] p-4 flex flex-col justify-between ${news.bg}`"
    >
      <div class="flex justify-end">
        <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
      </div>

      <div>
        <h6 class="text-white text-base font-medium w-full tracking-[0.32px] line-clamp-2">
          {{ news.title }}
        </h6>
      </div>
    </a>

    <!-- <div class="w-[288px] h-[335px] bg-new02 p-4 flex flex-col justify-between">
      <div class="flex justify-end">
        <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
      </div>

      <div>
        <h6 class="text-white text-base font-medium w-8/12 traking-[0.32px]">
          Price Impact of Unlock by Project
        </h6>
      </div>
    </div>

    <div class="w-[288px] h-[335px] bg-new01 p-4 flex flex-col justify-between">
      <div class="flex justify-end">
        <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
      </div>

      <div>
        <h6 class="text-white text-base font-medium w-8/12 traking-[0.32px]">
          Price Impact of Unlock by Project
        </h6>
      </div>
    </div>

    <div class="w-[288px] h-[335px] bg-new02 p-4 flex flex-col justify-between">
      <div class="flex justify-end">
        <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
      </div>

      <div>
        <h6 class="text-white text-base font-medium w-8/12 traking-[0.32px]">
          Price Impact of Unlock by Project
        </h6>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import inlineSvg from "vue-inline-svg";

import news1Img from "~/assets/images_new/landing/news/news-01_3x.webp";
import news2Img from "~/assets/images_new/landing/news/news-02_3x.webp";
import redirectIcon from "~/assets/images_new/landing/icons/arrow-right-up.svg";

defineProps({
  recentNews: {
    type: Array,
    required: true,
  },
});
</script>

<style lang="scss" scoped></style>
