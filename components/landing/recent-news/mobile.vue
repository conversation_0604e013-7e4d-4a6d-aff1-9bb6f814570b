<template>
  <div class="lg:hidden mb-[122px] overflow-hidden pl-5">
  <div class="-mx-4">
    <swiper
      class="recent-news-swiper"
      :modules="[Navigation]"
      :space-between="16"
      slidesPerView="auto"
      :breakpoints="{
        320: {
          spaceBetween: 16,
        },
        480: {
          spaceBetween: 16,
        },
        640: {
          spaceBetween: 16,
        },
      }"
    >
      <swiper-slide v-for="news in recentNews" class="min-w-[288px] max-w-[288px]">
        <a
          :href="news.link"
          target="_blank"
          :class="`h-[332px] p-4 flex flex-col justify-between rounded-xl ${news.bg}`"
        >
          <div class="flex justify-end">
            <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
          </div>

          <div>
            <h6
              class="text-white text-base font-medium w-full traking-[0.32px] line-clamp-2"
            >
              {{ news.title }}
            </h6>
          </div>
        </a>
      </swiper-slide>

      <!-- <swiper-slide class="min-w-[288px] max-w-[288px]">
        <div
          class="h-[332px] bg-new02 p-4 flex flex-col justify-between rounded-xl"
        >
          <div class="flex justify-end">
            <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
          </div>

          <div>
            <h6
              class="text-white text-base font-medium w-8/12 traking-[0.32px]"
            >
              Price Impact of Unlock by Project
            </h6>
          </div>
        </div>
      </swiper-slide>

      <swiper-slide class="min-w-[288px] max-w-[288px]">
        <div
          class="h-[332px] bg-new01 p-4 flex flex-col justify-between rounded-xl"
        >
          <div class="flex justify-end">
            <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
          </div>

          <div>
            <h6
              class="text-white text-base font-medium w-8/12 traking-[0.32px]"
            >
              Price Impact of Unlock by Project
            </h6>
          </div>
        </div>
      </swiper-slide>

      <swiper-slide class="min-w-[288px] max-w-[288px]">
        <div
          class="h-[332px] bg-new02 p-4 flex flex-col justify-between rounded-xl"
        >
          <div class="flex justify-end">
            <inline-svg class="w-[24px] h-[24px]" :src="redirectIcon" />
          </div>

          <div>
            <h6
              class="text-white text-base font-medium w-8/12 traking-[0.32px]"
            >
              Price Impact of Unlock by Project
            </h6>
          </div>
        </div>
      </swiper-slide> -->
    </swiper>
  </div>

  </div>
</template>

<script setup>
import inlineSvg from "vue-inline-svg";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation } from "swiper/modules";

import redirectIcon from "~/assets/images_new/landing/icons/arrow-right-up.svg";

// Add Swiper styles
import "swiper/swiper-bundle.css";

defineProps({
  recentNews: {
    type: Array,
    required: true,
  },
});
</script>

<style scoped>
.recent-news-swiper {
  margin: 0 16px;
}

:deep(.swiper-pagination) {
  bottom: -25px !important;
}

:deep(.swiper-pagination-bullet) {
  background: #ffffff;
  opacity: 0.5;
}

:deep(.swiper-pagination-bullet-active) {
  opacity: 1;
}
</style>
