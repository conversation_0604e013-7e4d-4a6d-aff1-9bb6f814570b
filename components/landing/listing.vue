<template>
  <div
    class="bg-[#0C1326] lg:border lg:border-white/10 rounded-2xl p-6 py-16 text-center relative overflow-hidden max-w-[1212px] max-h-[500px] mx-auto mb-[72px] lg:mb-[144px]"
  >
    <div class="absolute -bottom-[56px] -left-[50px] block lg:hidden">
      <img
        class="w-[112px] h-[193px]"
        :src="listingMobileImg"
        alt="listing-mobile"
      />
    </div>

    <div class="absolute -top-[53px] -left-[172px] hidden lg:block">
      <img
        class="w-[345.63px] h-[598.91px]"
        :src="listingWebImg"
        alt="listing-web"
      />
    </div>

    <div class="absolute top-[310px] -right-[122px] hidden lg:block">
      <img class="w-[284px] h-[491px]" :src="listingWebImg" alt="listing-web" />
    </div>

    <div
      class="bg-blur-primary absolute top-[294px] left-[48px] w-[175px] h-[248px] lg:top-[200px] lg:left-[400px] lg:w-[404px] lg:h-[573px]"
    ></div>

    <h6
      class="text-white text-sm lg:text-1.375xl font-normal tracking-[1.2px] mb-3"
    >
      Looking for Liquidity?
    </h6>
    <h1
      class="text-gradient text-3.5xl lg:text-7xl font-black mb-3 lg:leading-[80px]"
    >
      List Your Token on <br />
      SecondSwap
    </h1>
    <p
      class="text-white/60 text-sm lg:text-lg tracking-[0.24px] font-normal mb-8"
    >
      We offer a step-by-step guide for project teams to easily onboard and
      <br />
      list new tokens simple, safe, and transparent.
    </p>

    <a
      href="https://tally.so/r/mYr6d0"
      target="_blank"
      class="btn btn-primary w-[154px] lg:w-[240px] h-[30px] text-2sm font-medium relative"
    >
      List now
    </a>
  </div>
</template>

<script setup>
import listingWebImg from "~/assets/images_new/landing/listing/list-img-web_2x.webp";
import listingMobileImg from "~/assets/images_new/landing/listing/list-img-mobile_2x.webp";
</script>

<style scoped></style>
