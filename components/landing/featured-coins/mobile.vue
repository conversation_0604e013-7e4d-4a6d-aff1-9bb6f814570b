<template>
  <div class="relative w-full block lg:hidden">
    <div
      class="absolute left-0 -bottom-[80px] sm:-bottom-[120px] -mx-5 sm:-mx-0 overflow-hidden sm:w-full"
    >
      <img
        :src="featuredCoinsStage"
        alt="featured-coins-stage"
        class=""
      />
    </div>
    <!-- Add Swiper component -->
    <div class="swiper-container mx-auto mt-4 relative">
      <div class="bg-blur-primary absolute w-[231px] h-[342px]"></div>

      <img
        :src="startImg"
        alt="star"
        class="w-[22px] h-[25px] absolute top-[85px] right-[80px] z-10 transition-opacity duration-500"
        :style="{ opacity: nameOpacity }"
      />
      <img
        :src="startImg"
        alt="star"
        class="w-[41px] h-[47px] absolute top-0 left-[10px] transition-opacity duration-500"
        :style="{ opacity: nameOpacity }"
      />
      <img
        :src="startImg"
        alt="star"
        class="w-[22px] h-[25px] absolute -top-[30px] right-0 transition-opacity duration-500"
        :style="{ opacity: nameOpacity }"
      />

      <div class="flex flex-col justify-center items-center">
        <h5
          class="text-white text-2sm font-medium tracking-[3.08px] mb-1 transition-opacity duration-500"
          :style="{ opacity: nameOpacity }"
        >
          {{ activeTokenName }}
        </h5>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="1"
          height="54"
          viewBox="0 0 1 54"
          fill="none"
          class="transition-opacity duration-500"
          :style="{ opacity: nameOpacity }"
        >
          <path
            d="M0.499996 0.5L0.499998 53.5"
            stroke="url(#paint0_linear_4165_14288)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_4165_14288"
              x1="-0.5"
              y1="-12"
              x2="-0.500004"
              y2="78"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" stop-opacity="0.3" />
              <stop offset="1" stop-color="#090C1E" stop-opacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      <swiper
        :modules="[SwiperNavigation]"
        :navigation="{
          nextEl: '.swiper-button-next-1',
          prevEl: '.swiper-button-prev-1',
        }"
        :slides-per-view="1"
        :space-between="20"
        class="relative"
        :centeredSlides="true"
        :speed="800"
        :effect="'fade'"
        :fadeEffect="{
          crossFade: true,
        }"
        @slideChange="handleSlideChange"
        :loop="true"
      >
        <swiper-slide v-for="coin in coins" :key="coin.name">
          <div class="relative w-[201px] m-auto">
            <img
              :src="coin.image"
              :alt="coin.name"
              class="w-[201px] h-[201px] object-contain mx-auto"
            />
          </div>
        </swiper-slide>
      </swiper>
      <button
        class="swiper-button-prev swiper-button-prev-1 absolute left-0 sm:!left-[60px] md:!left-[150px] z-50 !mt-8"
      >
        <inline-svg class="w-[24px] h-[24px]" :src="arrowLeftIcon"></inline-svg>
      </button>
      <button
        class="swiper-button-next swiper-button-next-1 absolute right-0 sm:!right-[60px] md:!right-[150px] z-50 !mt-8"
      >
        <inline-svg
          class="w-[24px] h-[24px]"
          :src="arrowRightIcon"
        ></inline-svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import arrowLeftIcon from "~/assets/images_new/icons/arrow-left.svg";
import arrowRightIcon from "~/assets/images_new/icons/arrow-right.svg";

import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation, EffectFade } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/effect-fade";

import featuredCoinsStage from "~/assets/images_new/landing/featured_coins/featured_coins_stage_1x.webp";
import avaxImg from "~/assets/images_new/landing/coins/avax_3x.webp";
import hypeImg from "~/assets/images_new/landing/coins/hype_3x.webp";
import solImg from "~/assets/images_new/landing/coins/sol_3x.webp";
import scrollImg from "~/assets/images_new/landing/coins/scroll_3x.webp";
import xrpImg from "~/assets/images_new/landing/coins/xrp_3x.webp";
import zkImg from "~/assets/images_new/landing/coins/zk_3x.webp";

import startImg from "~/assets/images_new/landing/featured_coins/star_4x.webp";

const SwiperNavigation = Navigation;
const swiperModules = [Navigation, EffectFade];

const coins = [
  { name: "AVAX", image: avaxImg },
  { name: "HYPE", image: hypeImg },
  { name: "SOL", image: solImg },
  { name: "SCROLL", image: scrollImg },
  { name: "XRP", image: xrpImg },
  { name: "ZK", image: zkImg },
];

const activeTokenName = ref(coins[0].name);
const nameOpacity = ref(1);

const handleSlideChange = (swiper) => {
  // First set opacity to 0
  nameOpacity.value = 0;

  // Update the name and fade in after a short delay
  setTimeout(() => {
    activeTokenName.value = coins[swiper.realIndex].name;
    nameOpacity.value = 1;
  }, 400);
};
</script>

<style scoped>
.swiper-button-prev,
.swiper-button-next {
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  content: "";
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
  opacity: 0.5;
}

.bg-blur-primary {
  background: linear-gradient(180deg, #6baeff 0%, #1e89f5 100%);
  opacity: 0.3;
  filter: blur(100px);
  transform: rotate(-90deg);
}
</style>
