<template>
  <div
    class="relative w-full hidden lg:block featured-coins-section pt-[200px] overflow-hidden"
  >
    <div class="bg-blur-primary absolute bottom-0 w-[404px] h-[573px]"></div>

    <div class="absolute bottom-0 left-1/2 -translate-x-1/2 w-full">
      <img
        :src="featuredCoinsStageWeb"
        alt="featured-coins-stage"
        class="w-full max-w-[1571px] mx-auto scale-x-[1.05]"
      />
    </div>

    <div
      class="flex justify-center items-center pb-[180px] text-white relative -left-2"
    >
      <div
        class="z-20 relative transition-all sm:duration-500"
        :style="{ right: `${positions.hype}px` }"
      >
        <img
          :src="hypeImg"
          alt="hype"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <div
          class="absolute -bottom-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <inline-svg
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
          <h5 class="mt-4 tracking-[3.08px] text-2sm">HYPE</h5>
        </div>

        <img
          :src="startImg"
          alt="star"
          class="absolute -bottom-5 left-0 w-[22px] h-[25px] transition-opacity duration-500"
          :class="{ 'animate-fade': showStars, 'opacity-0': !showStars }"
        />
      </div>
      <div
        class="z-30 relative transition-all sm:duration-500"
        :style="{ right: `${positions.scroll}px` }"
      >
        <img
          :src="scrollImg"
          alt="scroll"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <div
          class="absolute -top-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <h5 class="mb-4 tracking-[3.08px] text-2sm">SCROLL</h5>
          <inline-svg
            class="rotate-180"
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
        </div>
      </div>
      <div
        class="z-40 relative transition-all sm:duration-500"
        :style="{ right: `${positions.sol}px` }"
      >
        <img
          :src="solImg"
          alt="solana"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />

        <div
          class="absolute -bottom-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <inline-svg
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
          <h5 class="mt-4 tracking-[3.08px] text-2sm">SOL</h5>
        </div>
      </div>
      <div class="z-50 ml-5 relative">
        <img
          :src="secondSwapImg"
          alt="secondswap"
          class="w-[302px] max-w-[302px] h-[302px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <img
          :src="startImg"
          alt="star"
          class="absolute top-0 left-0 w-[22px] h-[25px] transition-opacity duration-500"
          :class="{ 'animate-fade': showStars, 'opacity-0': !showStars }"
        />
      </div>
      <div
        class="z-40 relative transition-all sm:duration-500"
        :style="{ left: `${positions.xrp}px` }"
      >
        <img
          :src="xrpImg"
          alt="xrp"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <div
          class="absolute -bottom-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <inline-svg
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
          <h5 class="mt-4 tracking-[3.08px] text-2sm">XRP</h5>
        </div>
      </div>
      <div
        class="z-30 relative transition-all sm:duration-500"
        :style="{ left: `${positions.zk}px` }"
      >
        <img
          :src="zkImg"
          alt="zk"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <div
          class="absolute -top-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <h5 class="mb-4 tracking-[3.08px] text-2sm">ZK</h5>
          <inline-svg
            class="rotate-180"
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
        </div>
      </div>
      <div
        class="z-20 relative transition-all sm:duration-500"
        :style="{ left: `${positions.avax}px` }"
      >
        <img
          :src="avaxImg"
          alt="avalanche"
          class="w-[210px] max-w-[210px] h-[210px] filter drop-shadow-[0px_0px_44px_#01172C]"
        />
        <div
          class="absolute -bottom-[100px] left-1/2 -translate-x-1/2 flex flex-col items-center transition-opacity duration-500"
          :class="{ 'opacity-0': !showLabels, 'opacity-100': showLabels }"
        >
          <inline-svg
            :src="pointerSvg"
            :class="{ 'animate-fade': showLabels }"
          ></inline-svg>
          <h5 class="mt-4 tracking-[3.08px] text-2sm">AVAX</h5>
        </div>

        <img
          :src="startImg"
          alt="star"
          class="absolute top-1/2 -right-10 w-[22px] h-[25px] -translate-y-1/2 transition-opacity duration-500"
          :class="{ 'animate-fade': showStars, 'opacity-0': !showStars }"
        />
      </div>

    </div>

    <img
      :src="startImg"
      alt="star"
      class="absolute top-[50px] left-[30px] w-[49px] h-[56px] transition-opacity duration-500"
      :class="{ 'animate-fade': showStars, 'opacity-0': !showStars }"
    />
    <img
      :src="startImg"
      alt="star"
      class="absolute top-0 right-[30px] w-[49px] h-[56px] transition-opacity duration-500"
      :class="{ 'animate-fade': showStars, 'opacity-0': !showStars }"
    />
  </div>
</template>

<script setup>
import inlineSvg from "vue-inline-svg";

import pointerSvg from "~/assets/images_new/landing/featured_coins/featured-coins-pointer.svg";
import featuredCoinsStageWeb from "~/assets/images_new/landing/featured_coins/featured_coins_stage_web_2x.webp";
import secondSwapImg from "~/assets/images_new/landing/coins/secondswap_3x.webp";
import avaxImg from "~/assets/images_new/landing/coins/avax_3x.webp";
import hypeImg from "~/assets/images_new/landing/coins/hype_3x.webp";
import solImg from "~/assets/images_new/landing/coins/sol_3x.webp";
import scrollImg from "~/assets/images_new/landing/coins/scroll_3x.webp";
import xrpImg from "~/assets/images_new/landing/coins/xrp_3x.webp";
import zkImg from "~/assets/images_new/landing/coins/zk_3x.webp";

import startImg from "~/assets/images_new/landing/featured_coins/star_4x.webp";

import { reactive, onMounted, onUnmounted, ref } from "vue";

// Update positions with correct collapsed positions for right side
const positions = reactive({
  // Left side
  hype: -450,
  scroll: -305,
  sol: -160,
  // Right side
  xrp: -155, // Collapsed position
  zk: -300, // Collapsed position
  avax: -445, // Collapsed position
});

const showLabels = ref(false);
const showStars = ref(false);

// Update scroll handler with correct positions
onMounted(() => {
  const handleScroll = () => {
    const element = document.querySelector(".featured-coins-section");
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const isInViewport = rect.top < window.innerHeight && rect.bottom >= 0;

    if (isInViewport) {
      // Reset stars and labels
      // showStars.value = false;
      // showLabels.value = false;

      // Set positions first
      positions.hype = -185;
      positions.scroll = -125;
      positions.sol = -65;
      positions.xrp = -60;
      positions.zk = -120;
      positions.avax = -180;

      // Show stars immediately with expansion
      setTimeout(() => {
        showStars.value = true;
      }, 100);

      // Show labels after delay
      setTimeout(() => {
        showLabels.value = true;
      }, 1500);
    } else {
      // Reset everything
      positions.hype = -450;
      positions.scroll = -305;
      positions.sol = -160;
      positions.xrp = -155;
      positions.zk = -300;
      positions.avax = -445;

      showStars.value = false;
      showLabels.value = false;
    }
  };

  window.addEventListener("scroll", handleScroll);
  handleScroll(); // Check initial position

  onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
  });
});
</script>

<style scoped>
.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.bg-blur-primary {
  position: absolute;
  left: 35.35%;
  right: 45.93%;
  background: linear-gradient(180deg, #6baeff 2.36%, #1e89f5 102.36%);
  opacity: 0.3;
  filter: blur(100px);
  transform: rotate(-90deg);
}
</style>
