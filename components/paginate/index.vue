<script setup>
import "vue-awesome-paginate/dist/style.css";

const props = defineProps({
  totalItems: Number,
});

const emit = defineEmits(["callback"]);

const onClickHandler = (page) => {
  console.log(page);
  emit("callback");
};

const currentPage = ref(1);
const itemsPerPage = 10;
const maxPagesShown = 10;

console.log("paginate props", props);

defineExpose({
  currentPage,
  itemsPerPage,
  maxPagesShown,
});
</script>

<template>
  <div class="flex justify-center mt-8">
    <vue-awesome-paginate
      :total-items="totalItems"
      :items-per-page="itemsPerPage"
      :max-pages-shown="maxPagesShown"
      hidePrevNextWhenEnds="true"
      v-model="currentPage"
      @click="onClickHandler"
    />
  </div>
</template>

<style>
.pagination-container {
  display: flex;
  column-gap: 5px;
}

.paginate-buttons {
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 10px;
}

.paginate-buttons:hover {
  border: 1px solid white;
}

.active-page {
  border: 1px solid white;
  color: white;
  border-radius: 10px;
}

/* .active-page:hover {
  background-color: #2988c8;
} */
</style>