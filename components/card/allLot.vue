<template>
  <div class="card-lot relative bg-white/[6%] rounded-xl group">
    <!-- <div
      
      class="absolute top-0 left-0 flex items-end w-full h-full p-4 overflow-hidden transition-all duration-500 opacity-0 bg-backdrop backdrop-blur-sm bg-white/5 rounded-xl"
    >
      <button
        @click="$emit('showBuyModalEvent')"
        class="relative w-full h-10 transition-all duration-300 btn btn-primary min-h-10 -bottom-20 group-hover:bottom-0 font-satoshiRegular"
      >
        Buy Now
      </button>
    </div> -->

    <div class="relative collapse">
      <input
        @click.stop="toggleDropdown(index)"
        type="checkbox"
        :class="
          isOpen[index]
            ? 'absolute w-1/5 top-20 right-0'
            : 'absolute w-1/5 bottom-0 right-0'
        "
      />
      <div class="collapse-title pr-4 py-5 h-[142px]">
        <div class="flex justify-between mb-4">
          <div class="relative w-[44px] h-[44px] flex-shrink-0">
            <img
              class="w-full h-full rounded-full coin-border"
              :src="tokenImg"
              alt="token-img"
            />

            <img
              class="absolute bottom-0 w-[16px] h-[16px] rounded-full"
              :src="networkImg"
              alt="network-img"
            />
          </div>
          <div
            v-if="progressValue == 100"
            class="flex items-center justify-center border-[1.5px] rounded-full border-input-icons/[8%] w-11 h-11"
          >
            <div class="text-xs text-white/20">0%</div>
          </div>
          <div v-else class="relative flex items-center justify-center">
            <!-- Background progress (first color) -->
            <div
              class="text-xs font-medium radial-progress text-success"
              :style="`--value: ${
                100 - progressValue
              }; --size: 44px; --thickness: 1.5px`"
            >
              <div class="text-success">
                {{
                  progressValue == 100
                    ? 0
                    : numeral(100 - progressValue).format("0,0")
                }}%
              </div>
            </div>
            <!-- Foreground progress (second color) -->
            <div
              class="absolute radial-progress text-success/10"
              style="--value: 100; --size: 44px; --thickness: 1px"
            ></div>
          </div>
        </div>
        <div class="">
          <div class="flex items-end gap-1 mb-1 lg:items-center">
            <h5 class="text-sm">{{ tokenName }}</h5>
            <h6
              class="text-xs text-white/50 font-satoshiLight lg:font-satoshiRegular"
            >
              #{{ lotId }}
            </h6>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-end gap-1 lg:items-center">
              <h3>{{ formatUSDT(tokenPrice) }} USDT</h3>
              <!-- GET FROM BACKEND -->
              <h5 :class="`${twfHourColor} text-sm`">{{ twfHourChanges }}</h5>
            </div>
            <label for="index">
              <inline-svg
                width="14"
                :src="chevronDown"
                :style="{
                  transform: isOpen[index] ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease',
                }"
              />
            </label>
          </div>
        </div>
      </div>
      <div class="collapse-content">
        <div class="flex flex-col gap-3 mt-2 mb-5 text-sm font-medium">
          <div class="flex items-center justify-between">
            <h5 class="text-white/50">Best Price</h5>
            <div class="flex items-center gap-1">
              <h5>{{ formatUSDT(bestPrice) }}</h5>
              <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <h5 class="text-white/50">Max Discount</h5>
            <h5>Up to {{ numeral(maxDiscount).format("0.00") }}%</h5>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center gap-1">
              <h5 class="text-white/50">Unlock Start</h5>
              <inline-svg :src="infoIcon"></inline-svg>
            </div>

            <div :class="countDown ? 'gap-1' : ''" class="flex items-center">
              <h5>{{ $dayjs(unlockStart * 1).format("DD/M/YYYY") }}</h5>
              <h5 class="text-xs text-success">{{ countDown }}</h5>
            </div>
          </div>
        </div>

        <button
          @click.stop="handleButtonClick"
          class="w-full h-10 font-medium btn btn-primary min-h-10"
        >
          {{ buttonText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import secondSwapToken from "~/assets/images_new/tokens/2swap_1_5x.webp";
import ethNetwork from "~/assets/images_new/networks/eth_4x.webp";
import chevronDown from "~/assets/images_new/icons/chevron-down.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";

import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import { useRouter, useRoute } from "vue-router";
import { formatUSDT } from "~/utils/number";

const { $dayjs } = useNuxtApp();

const props = defineProps({
  tokenImg: String,
  networkImg: String,
  networkSymbol: String,
  totalListing: String,
  remainingListing: String,
  tokenName: String,
  tokenTicker: String,
  listId: String,
  tokenPrice: String,
  bestPrice: String,
  maxDiscount: String,
  unlockStart: String,
  displayId: String,
  twfHourChanges: String,
  twfHourColor: String,
  lotId: String,
  index: Number,
  buttonText: String,
  planId: String,
});

const { refetch } = useMarketplace()

const progressValue = computed(
  () => (props.remainingListing / props.totalListing) * 100
);

const countDown = computed(() => {
  const unlockStart = $dayjs(props.unlockStart * 1);
  const diffInMs = unlockStart.diff($dayjs());
  if (diffInMs <= 0) {
    return null;
  }

  const durationObj = $dayjs.duration(diffInMs);

  const days = Math.floor(durationObj.asDays()); // Total days
  const hours = Math.floor(durationObj.days()); // Remaining hours after extracting days
  const minutes = durationObj.minutes().toString().padStart(2, "0");

  return `${days}d : ${hours}h : ${minutes}m`;
});

const isOpen = ref([]);

function toggleDropdown(index) {
  isOpen.value[index] = !isOpen.value[index];
}

const router = useRouter();
const route = useRoute();

const emit = defineEmits(["showBuyModalEvent"]);

async function handleButtonClick() {
  console.log("route", route.name);
  if (route.name === "marketplace-lot") {
    router.push(
      `/${props.planId}-${props.displayId}-${props.listId}?network=${props.networkSymbol}`
    );
  } else {
    const refetched = await refetch()
    const foundLot = refetched.allLots?.find(refetchedLot => {
      // TECH DEPT: we need a better unique identifier to find the lot
      return props.lotId === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && props.tokenImg === refetchedLot.token_image
    })
    emit("showBuyModalEvent", { lot: foundLot });
  }
}
</script>

<style scoped>
.card-lot:hover .bg-backdrop {
  opacity: 1;
  z-index: 50;
}
</style>
