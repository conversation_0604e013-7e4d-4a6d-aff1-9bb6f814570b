<template>
  <div
    class="card-lot w-[295px] h-[306px] lg:h-[246px] relative bg-secondary p-4 py-5 rounded-xl group"
  >
    <div class="hidden lg:block">
      <div
        class="absolute top-0 left-0 flex items-end w-full h-full p-4 overflow-hidden transition-all duration-500 opacity-0 bg-backdrop backdrop-blur-sm bg-white/5 rounded-xl"
      >
        <button
          @click="handleClickAction"
          class="relative w-full h-10 font-medium transition-all duration-300 btn btn-primary min-h-10 -bottom-20 group-hover:bottom-0"
        >
          {{ web3Store.userInfo.isConnected ? "Buy" : "Connect" }}
        </button>
      </div>
    </div>

    <div class="flex items-center justify-between mb-4">
      <div class="relative w-[44px] h-[44px] flex-shrink-0">
        <img
          class="w-full h-full rounded-full coin-border"
          :src="tokenImg"
          alt="token-img"
        />

        <img
          class="absolute bottom-0 w-[16px] h-[16px] rounded-full"
          :src="networkImg"
          alt="network-img"
        />
      </div>

      <div v-if="listingType == 0">
        <div
          v-if="progressValue == 100"
          class="flex items-center justify-center border-[1.5px] rounded-full border-input-icons/[8%] w-11 h-11"
        >
          <div class="text-xs text-white/20">0%</div>
        </div>

        <div v-else class="relative flex items-center justify-center">
          <!-- Background progress (first color) -->
          <div
            class="text-xs font-medium radial-progress text-success"
            :style="`--value: ${
              100 - progressValue
            }; --size: 44px; --thickness: 1.5px`"
          >
            <div class="text-success">
              {{
                progressValue == 100
                  ? 0
                  : numeral(100 - progressValue).format("0")
              }}%
            </div>

            <!-- {{ progressValue == 100 ? progressValue : numeral(progressValue).format("0.00") }}% -->
          </div>
          <!-- Foreground progress (second color) -->
          <div
            class="absolute radial-progress text-success/10"
            style="--value: 100; --size: 44px; --thickness: 1px"
          ></div>
        </div>
      </div>

      <div v-else>
        <div
          class="flex items-center justify-center px-2 py-1 rounded-[4px] bg-white/[0.08]"
        >
          <div class="text-xs text-white/50">Single Fill</div>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <div class="flex items-center gap-1 mb-1">
        <h5 class="text-sm leading-4">{{ tokenName }}</h5>
        <h6 class="text-xs text-white/50 font-satoshiLight leading-[15px]">
          #{{ lotId }}
        </h6>
      </div>
      <div class="flex items-center gap-1 mb-1">
        <h3 class="leading-[22px] text-base">
          <!-- {{ common.formatUSDT(tokenPrice) }} -->
          {{ formatUSDT(bestPrice) }} USDT
        </h3>
        <!-- <h5 :class="`${twfHourColor} text-sm leading-4`">
          {{ twfHourChanges }}
        </h5> -->
      </div>
    </div>

    <div class="flex flex-col gap-3 lg:gap-2.5 text-sm font-medium leading-4">
      <div class="flex items-center justify-between">
        <h5 class="text-white/50">Available</h5>
        <div class="flex items-center gap-1">
          <h5 class="pt-[2px]">
            {{
              formatToken(
                divideNumberUsingDecimals(remainingListing, tokenDecimal)
              )
            }}
          </h5>
        </div>
      </div>
      <!-- <div class="flex items-center justify-between">
        <h5 class="text-white/50">Best Price</h5>
        <div class="flex items-center gap-1">
          <h5 class="pt-[2px]">
            {{ common.formatUSDT(bestPrice) }}
          </h5>
          <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
        </div>
      </div> -->

      <div class="flex items-center justify-between">
        <h5 class="text-white/50">Max Discount To Spot</h5>
        <!-- <h5>Up to {{ numeral(maxDiscount).format("0.00") }}%</h5> -->
        <h5 class="text-white/50" v-if="maxDiscountToSpot > 0">
          +{{ numeral(maxDiscountToSpot).format("0.00") }}%
        </h5>
        <h5 v-else-if="maxDiscountToSpot < 0">
          Up to
          <span class="text-success">{{ numeral(maxDiscountToSpot.replace("-", "")).format("0.00") }}%</span>
        </h5>
        <h5 class="text-white/50" v-else>---</h5>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center gap-1">
          <h5 class="text-white/50">Unlock Start</h5>
          <!-- <inline-svg :src="infoIcon"></inline-svg> -->
        </div>
        <div class="flex items-center gap-1">
          <h5>{{ $dayjs(unlockStart * 1).format("DD/M/YYYY") }}</h5>
          <h5 class="text-xs text-success">{{ countDown }}</h5>
        </div>
      </div>
      <button
        @click="handleClickAction"
        class="w-full h-10 mt-2 font-medium btn btn-primary min-h-10 lg:hidden"
      >
        {{ web3Store.userInfo.isConnected ? "Buy" : "Connect" }}
      </button>
    </div>
  </div>
</template>

<script setup>
import secondSwapToken from "~/assets/images_new/tokens/2swap_1_5x.webp";
import ethNetwork from "~/assets/images_new/networks/eth_4x.webp";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import infoIcon from "~/assets/images_new/icons/info.svg";

import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import { divideNumberUsingDecimals, formatUSDT, formatToken } from "~/utils/number";

const { $dayjs } = useNuxtApp();
const web3Store = useWeb3Store();
const { refetch } = useMarketplace()

const props = defineProps({
  tokenImg: String,
  networkImg: String,
  networkSymbol: String,
  totalListing: String,
  remainingListing: String,
  tokenName: String,
  tokenTicker: String,
  listId: String,
  listingType: String,
  tokenPrice: String,
  bestPrice: String,
  maxDiscount: String,
  unlockStart: String,
  displayId: String,
  twfHourChanges: String,
  twfHourColor: String,
  lotId: String,
  tokenDecimal: String,
});

const emit = defineEmits(["showBuyModalEvent"])

const progressValue = computed(
  () => (props.remainingListing / props.totalListing) * 100
);

const countDown = computed(() => {
  const unlockStart = $dayjs.utc(props.unlockStart * 1);
  const diffInMs = unlockStart.diff($dayjs.utc());
  if (diffInMs <= 0) {
    return null;
  }

  const durationObj = $dayjs.duration(diffInMs);

  const days = Math.floor(durationObj.asDays()); // Total days
  const hours = Math.floor(durationObj.hours()); // Remaining hours after extracting days
  const minutes = durationObj.minutes().toString().padStart(2, "0");

  return `${days}d : ${hours}h : ${minutes}m`;
});

const maxDiscountToSpot = computed(() => {
  // Convert string values to numbers and ensure they're not zero
  const best = Number(props.bestPrice);
  const token = Number((props.tokenPrice));

  console.log("best", best);
  console.log("best token", token, props.tokenDecimal);

  if (!best || !token) return 0;

  // Calculate percentage difference
  return (((best - token) / token) * 100).toFixed(2);
});

const handleClickAction = async () => {
  await web3Store.switchNetwork(props.networkSymbol)
  const refetched = await refetch()
  const foundLot = refetched.allLots?.find(refetchedLot => {
    // TECH DEPT: we need a better unique identifier to find the lot
    return props.lotId === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && props.tokenImg === refetchedLot.token_image
  })

  emit('showBuyModalEvent', { lot: foundLot })
}

// function showBuyModal() {
//   document.getElementById("buyModal").showModal();
// }
</script>

<style scoped>
.card-lot:hover .bg-backdrop {
  opacity: 1;
  z-index: 50;
}
</style>
