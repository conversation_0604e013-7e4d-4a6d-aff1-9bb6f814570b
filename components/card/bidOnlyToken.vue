<template>
  <div
    class="card-token h-[189px] relative bg-secondary p-4 py-5 rounded-xl hover:translate-y-[-10px] transition-all duration-300"
  >
    <!-- <div
      class="absolute top-0 left-0 flex items-end w-full h-full p-4 transition-all duration-500 opacity-0 bg-backdrop backdrop-blur-sm bg-white/5 rounded-xl"
    >
      <button class="w-full h-8 btn btn-primary min-h-8">Buy</button>
    </div> -->

    <div class="flex gap-4 mb-8">
      <div class="relative w-[44px] h-[44px] flex-shrink-0">
        <img
          class="w-full h-full rounded-full coin-border"
          :src="tokenImg"
          alt="token-img"
        />

        <img
          class="absolute bottom-0 w-[16px] h-[16px] rounded-full"
          :src="networkImg"
          alt="network-img"
        />
      </div>

      <div>
        <div class="flex items-center gap-1">
          <h5>{{ tokenName }}</h5>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-3 text-sm font-medium leading-4">
      <div class="flex items-center justify-between">
        <h5 class="text-white/50">Market Cap</h5>
        <h5 class="uppercase">{{ numeral(marketCap).format("0,0.00a") }}</h5>
      </div>

      <div class="flex items-center justify-between">
        <h5 class="text-white/50">Circulating Supply</h5>
        <h5 class="uppercase">{{ numeral(circulatingSupply).format("0,0.00a") }}</h5>
      </div>
    </div>
  </div>
</template>

<script setup>
import numeral from "numeral";

const { $dayjs } = useNuxtApp();

const props = defineProps({
  tokenImg: String,
  networkImg: String,
  tokenName: String,
  tokenTicker: String,
  marketCap: String,
  circulatingSupply: String,
});

const countDown = computed(() => {
  const unlockStart = $dayjs(props.shortestUnlock * 1);
  const diffInMs = unlockStart.diff($dayjs());

  if (diffInMs <= 0) {
    return null;
  }

  const durationObj = $dayjs.duration(diffInMs);

  const days = Math.floor(durationObj.asDays()); // Total days
  const hours = Math.floor(durationObj.days()); // Remaining hours after extracting days
  const minutes = durationObj.minutes().toString().padStart(2, "0");

  return `${days}d : ${hours}h : ${minutes}m`;
});
</script>

<style scoped>
.card-token:hover .bg-backdrop {
  opacity: 1;
  z-index: 50;
}
</style>
