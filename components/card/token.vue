<template>
  <div
    class="card-token h-[189px] w-[295px] relative bg-secondary p-4 py-5 rounded-xl hover:translate-y-[-10px] transition-all duration-300"
  >
    <!-- <div
      class="bg-backdrop p-4 flex items-end absolute backdrop-blur-sm bg-white/5 w-full h-full top-0 left-0 rounded-xl opacity-0 transition-all duration-500"
    >
      <button class="btn btn-primary w-full h-8 min-h-8">Buy</button>
    </div> -->

    <div class="flex gap-4 mb-8">
      <div class="relative w-[44px] h-[44px] flex-shrink-0">
        <img
          class="w-full h-full rounded-full coin-border"
          :src="tokenImg"
          alt="token-img"
        />

        <img
          class="absolute bottom-0 w-[16px] h-[16px] rounded-full"
          :src="networkImg"
          alt="network-img"
        />
      </div>

      <div>
        <div class="flex items-center gap-1">
          <h5 class="text-sm">{{ tokenName }}</h5>
        </div>
        <div class="flex items-center gap-1">
          <h3>
            <!-- {{
              common.formatSmallNumber(
                numeral(tokenPrice).format(
                  useRuntimeConfig().public.usdtDecimal,
                  Math.floor
                )
              )
            }} -->
            {{ formatUSDT(tokenPrice) }}

            USDT
          </h3>
          <h5 :class="`${twfHourColor} text-sm`">{{ twfHourChanges }}</h5>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-3 text-sm font-medium leading-4">
      <div class="flex items-center justify-between">
        <h5 class="text-white/50 upp">Total Available</h5>
        <h5 class="uppercase">{{ numeral(totalAvailable).format("0.00a") }}</h5>
      </div>

      <div class="flex items-center justify-between">
        <h5 class="text-white/50">Market Cap</h5>
        <h5 class="uppercase">{{ numeral(marketCap).format("0.00a") }}</h5>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center gap-1">
          <h5 class="text-white/50">Shortest Unlock</h5>
          <!-- <inline-svg :src="infoIcon"></inline-svg> -->
        </div>

        <div class="flex items-center" :class="countDown ? 'gap-1' : ''">
          <h5>{{ $dayjs(shortestUnlock * 1).format("DD/M/YYYY") }}</h5>
          <h5 class="text-success text-xs">{{ countDown }}</h5>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import infoIcon from "~/assets/images_new/icons/info.svg";
import numeral from "numeral";
import InlineSvg from "vue-inline-svg";
import { formatUSDT } from "~/utils/number";

const { $dayjs } = useNuxtApp();

const props = defineProps({
  tokenImg: String,
  networkImg: String,
  tokenName: String,
  tokenTicker: String,
  tokenPrice: String,
  totalAvailable: String,
  marketCap: String,
  shortestUnlock: String,
  twfHourChanges: String,
  twfHourColor: String,
  tokenDecimal: String,
});

const countDown = computed(() => {
  const unlockStart = $dayjs.utc(props.shortestUnlock * 1);
  const diffInMs = unlockStart.diff($dayjs.utc());

  if (diffInMs <= 0) {
    return null;
  }

  const durationObj = $dayjs.duration(diffInMs);

  const days = Math.floor(durationObj.asDays()); // Total days
  const hours = Math.floor(durationObj.hours()); // Remaining hours after extracting days
  const minutes = durationObj.minutes().toString().padStart(2, "0");

  return `${days}d : ${hours}h : ${minutes}m`;
});
</script>

<style scoped>
.card-token:hover .bg-backdrop {
  opacity: 1;
  z-index: 50;
}
</style>
