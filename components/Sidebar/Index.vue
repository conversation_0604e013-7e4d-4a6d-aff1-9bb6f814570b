<template>
  <div
    class="sidebar overflow-visible fixed bg-accent h-full w-20 text-white text-center p-4 hover:w-52 transition-all z-50"
  >
    <div class="h-full">
      <div class="flex flex-col justify-between h-full">
        <div class="flex flex-col gap-8 pt-2">
          <div class="flex items-center p-3 gap-2">
            <img class="w-[24px] h-[33px]" :src="secondswapLogo" alt="" />
            <!-- <img
            class="sidebar-hidden-item hidden w-[170px] h-[33px]"
            :src="secondswapFullLogo"
            alt=""
          /> -->
            <h5 class="sidebar-hidden-item text-xl opacity-0 whitespace-nowrap">
              SecondSwap
            </h5>
          </div>

          <NuxtLink
            class="flex gap-2 p-3 rounded-lg hover:bg-primary/10 group"
            to="/"
            active-class="text-primary"
          >
            <div>
              <inline-svg
                :class="{
                  'fill-primary group-hover:fill-primary':
                    $route.path === '/',
                  'fill-white group-hover:fill-white':
                    $route.path !== '/',
                }"
                :src="marketplaceIcon"
                width="24"
                height="24"
              ></inline-svg>
            </div>
            <h5 class="sidebar-hidden-item opacity-0 whitespace-nowrap">
              Marketplace
            </h5>
          </NuxtLink>

          <NuxtLink
            class="flex gap-2 p-3 rounded-lg hover:bg-primary/10 group cursor-pointer"
            :class="{
              'text-primary hover:text-primary': $route.path === '/myassets',
              'text-white hover:text-white': $route.path !== '/myassets',
            }"
            @click="handleClick"
            v-if="useRuntimeConfig().public.bidOnly != 'true'"
          >
            <div>
              <inline-svg
                :class="{
                  'fill-primary group-hover:fill-primary':
                    $route.path === '/myassets',
                  'fill-white group-hover:fill-white':
                    $route.path !== '/myassets',
                }"
                :src="walletIcon"
                width="24"
                height="24"
              ></inline-svg>
            </div>
            <h5 class="sidebar-hidden-item opacity-0 whitespace-nowrap">
              My Assets
            </h5>
          </NuxtLink>

          <!--  <div class="dropdown dropdown-hover dropdown-right">
            <div
              tabindex="0"
              role="button"
              class="flex gap-2 p-3 rounded-lg hover:bg-primary/10 hover:fill-primary group"
            >
              <div class="flex items-center justify-between w-full">
                <div class="flex gap-2">
                  <inline-svg
                    class="fill-white"
                    :src="walletIcon"
                    width="24"
                    height="24"
                  ></inline-svg>
                  <h5 class="sidebar-hidden-item opacity-0 whitespace-nowrap">
                    My Assets
                  </h5>
                </div>
                <inline-svg
                  class="sidebar-hidden-item opacity-0"
                  :src="chevronRightIcon"
                  width="16"
                  height="16"
                ></inline-svg>
              </div>
            </div>

            <div tabindex="0" class="dropdown-content z-[100] menu mt-0">
              <ul
                class="p-2 shadow bg-accent rounded-[8px] w-36 h-[111px] ml-3 border border-white/20 flex flex-col justify-center gap-2"
              >
                <li class="group">
                  <a class="text-white flex gap-2 hover:bg-primary/10">
                    <inline-svg
                      class="fill-white"
                      :src="tokenIcon"
                      width="20"
                      height="20"
                    ></inline-svg>
                    Token
                  </a>
                </li>
                <li class="group">
                  <a class="text-white flex gap-2 hover:bg-primary/10">
                    <inline-svg
                      class="fill-white"
                      :src="listedLotIcon"
                      width="20"
                      height="20"
                    ></inline-svg>
                    Listed Lot
                  </a>
                </li>
              </ul>
            </div> 
          </div>-->

          <div
            class="flex justify-between items-center gap-2 p-3 rounded-lg hover:bg-primary/10 hover:fill-primary group"
          >
            <div class="flex gap-2">
              <div>
                <inline-svg
                  class="fill-white"
                  :src="referralIcon"
                  width="24"
                  height="24"
                ></inline-svg>
              </div>
              <h5 class="sidebar-hidden-item opacity-0 whitespace-nowrap">
                Referral
              </h5>
            </div>

            <h5
              class="sidebar-hidden-item hidden text-xs bg-white/5 px-2 py-1 text-white/50 text-center rounded-md"
            >
              SOON
            </h5>
          </div>
        </div>

        <div class="flex justify-center">
          <img
            class="w-[30px] h-[30px] sidebar-show-item"
            :src="helpIcon"
            alt=""
          />
          <button
            class="sidebar-hidden-item opacity-0 hidden btn border-primary bg-transparent !rounded-[8px] whitespace-nowrap h-10 min-h-10 w-full hover:border-primary hover:bg-transparent"
            @click="openCrispChat"
          >
            <span class="text-white">Need Help?</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-overlay hidden fixed bg-black/60 w-full h-full z-40"></div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import secondswapLogo from "~/assets/images_new/common/2s-logo_1_5x.webp";
import secondswapFullLogo from "~/assets/images_new/common/2s-full-logo_1_5x.webp";
import marketplaceIcon from "~/assets/images_new/icons/marketplace.svg";
import walletIcon from "~/assets/images_new/icons/wallet.svg";
import referralIcon from "~/assets/images_new/icons/referral.svg";
import tokenIcon from "~/assets/images_new/icons/token.svg";
import listedLotIcon from "~/assets/images_new/icons/listed-lot.svg";
import chevronRightIcon from "~/assets/images_new/icons/chevron-right.svg";

import helpIcon from "~/assets/images_new/common/help-icon_1_5x.webp";
import { useAppKitAccount } from "@reown/appkit/vue";

const account = useAppKitAccount()
const web3Store = useWeb3Store();

const route = useRoute();
const router = useRouter();

const openCrispChat = () => {
  if (window.$crisp) {
    // window.$crisp.push(["do", "chat:toggle"]); // Show the widget
    // window.$crisp.push(["do", "chat:open"]); // Open the chat

    if (window.$crisp.is("chat:opened")) {
      window.$crisp.push(["do", "chat:close"]);
      window.$crisp.push(["do", "chat:hide"]);
    } else {
      window.$crisp.push(["do", "chat:show"]);
      window.$crisp.push(["do", "chat:open"]);
    }
  } else {
    console.warn("Crisp chat is not initialized");
  }
};

function handleClick() {
  if (web3Store.userInfo.isConnected || account.value.isConnected) {
    router.push("/myassets");
  } else {
    document.getElementById("connectModal").checked = true;
  }
}
</script>

<style scoped>
.bg-overlay {
  display: none;
}

.sidebar:hover ~ .bg-overlay {
  display: block;
}

.sidebar:hover .sidebar-show-item {
  display: none;
  opacity: 1;
}

.sidebar:hover .sidebar-hidden-item {
  transition: 500ms;
  display: block;
  opacity: 1;
}

.sidebar:not(:hover) .dropdown-content {
  display: none;
}
</style>
