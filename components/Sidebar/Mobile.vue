<template>
  <Teleport to="body">
    <input type="checkbox" id="mobileSidebar" class="modal-toggle" />

    <div class="modal lg:hidden" role="dialog">
      <div
        :class="[
          isMobile
            ? isMounted
              ? 'translate-y-0 transform transition-all duration-500 ease-out'
              : 'translate-y-full transform transition-all duration-500 ease-out'
            : '',
          'md:translate-y-0',
        ]"
        class="font-medium px-5 py-8 overflow-hidden bg-modal fixed bottom-0 w-full transform transition-all duration-500 ease-out !rounded-none"
      >
        <div class="flex justify-between mb-8 px-3">
          <div class="flex items-center gap-[3px] h-[33px]">
            <img class="w-[24px] h-[30px]" :src="secondswapLogo" alt="" />
            <h5 class="text-xl text-white">SecondSwap</h5>
          </div>
          <label
            for="mobileSidebar"
            @click="handleClose"
            class="btn btn-sm text-lg p-0 btn-circle btn-ghost"
          >
            ✕
          </label>
        </div>
        <div class="flex flex-col gap-3 mb-10 px-3">
          <NuxtLink
            class="flex gap-2 rounded-lg hover:bg-primary/10 group h-12 items-center"
            :class="{
              'text-primary group-hover:text-primary':
                $route.path === '/',
              'text-white group-hover:text-white':
                $route.path !== '/',
            }"
            to="/"
            active-class="text-primary"
            @click="handleRouting"
          >
            <div>
              <inline-svg
                :class="{
                  'fill-primary group-hover:fill-primary':
                    $route.path === '/',
                  'fill-white group-hover:fill-white':
                    $route.path !== '/',
                }"
                :src="marketplaceIcon"
                width="24"
                height="24"
              ></inline-svg>
            </div>
            <h5 class="">Marketplace</h5>
          </NuxtLink>
          <NuxtLink
            v-if="useRuntimeConfig().public.bidOnly != 'true'"
            to="/myassets"
            class="flex gap-3 rounded-lg hover:bg-primary/10 group cursor-pointer h-12 items-center"
            :class="{
              'text-primary hover:text-primary': $route.path === '/myassets',
              'text-white hover:text-white': $route.path !== '/myassets',
            }"
            @click="handleRouting"
          >
            <div>
              <inline-svg
                :class="{
                  'fill-primary group-hover:fill-primary':
                    $route.path === '/myassets',
                  'fill-white group-hover:fill-white':
                    $route.path !== '/myassets',
                }"
                :src="walletIcon"
                width="24"
                height="24"
              ></inline-svg>
            </div>
            <h5 class="">My Assets</h5>
          </NuxtLink>
          <div
            class="flex justify-between items-center gap-3 rounded-lg hover:bg-primary/10 hover:fill-primary hover:text-primary group h-12"
          >
            <div class="flex gap-2">
              <div>
                <inline-svg
                  class="fill-white"
                  :src="referralIcon"
                  width="24"
                  height="24"
                ></inline-svg>
              </div>
              <h5 class="text-white">Referral</h5>
            </div>

            <h5
              class="text-xs bg-white/5 px-2 py-1 text-white/50 text-center rounded-md"
            >
              SOON
            </h5>
          </div>
        </div>
        <div v-if="!web3Store.userInfo.isConnected">
          <button
            @click="callConnectModal"
            class="btn btn-primary !rounded-full w-full"
          >
            Connect
          </button>
        </div>
        <div v-else>
          <div
            class="btn bg-primary/20 p-2 lg:px-4 rounded-full flex items-center justify-center gap-2 cursor-pointer w-full"
            @click="web3Store.connectWallet()"
          >
            <div class="w-[22px] h-[22px] bg-primary rounded-full"></div>
            <h5>
              {{ common.formatAddress(web3Store.userInfo.address) }}
            </h5>
            <inline-svg :src="arrowDown" class="hidden lg:block"></inline-svg>
          </div>
        </div>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="connectModal"
        >Close</label
      >
    </div>
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import secondswapLogo from "~/assets/images_new/common/2s-logo_1_5x.webp";
import marketplaceIcon from "~/assets/images_new/icons/marketplace.svg";
import walletIcon from "~/assets/images_new/icons/wallet.svg";
import referralIcon from "~/assets/images_new/icons/referral.svg";

const web3Store = useWeb3Store();

const route = useRoute();
const router = useRouter();
const emit = defineEmits(["callConnectModalEvent"]);

// modal function
const isMounted = ref(false);
const isMobile = ref(false);

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
    document.body.style.overflow = "auto";
    document.body.style.position = "";
  }
  console.log("close modal executed isMobile", isMobile.value);
};

const handleClose = () => {
  // document.getElementById("mobileSidebar").checked = false;
  closeModal();
  // resetForm();
};

function callConnectModal() {
  handleRouting();
  emit("callConnectModalEvent");
}

function handleRouting() {
  document.getElementById("mobileSidebar").checked = false;
  closeModal();
}

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

defineExpose({
  handleClick,
});
</script>

<style scoped></style>
