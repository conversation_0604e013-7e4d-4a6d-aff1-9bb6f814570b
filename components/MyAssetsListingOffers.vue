<template>
	<NetworkTabs v-model="network" />
	<div class="overflow-auto">
		<table v-if="list.isSuccess" class="table border-separate text-2sm border-spacing-y-3">
			<thead class="text-sm">
				<tr class="border-b-0">
					<th class="w-1/6 pl-5 font-normal">Token</th>
					<th class="w-1/6 pl-5 font-normal">Price</th>
					<th class="w-1/6 pl-5 font-normal">Quantity</th>
					<th class="w-1/6 pl-5 font-normal">Unlock Date</th>
					<th class="w-1/6 pl-5 font-normal"></th>
				</tr>
			</thead>
			<tbody>
				<tr v-if="list.isSuccess && orders.length > 0" v-for="item in orders" class="border-b-0" :key="item.id">
					<td class="pl-5">
						<div class="flex items-center gap-4">
							<div class="relative w-[44px] h-[44px] flex-shrink-0">
								<img class="w-full h-full rounded-full coin-border" :src="item.token.image" alt="token-img" />

								<img class="absolute bottom-0 w-[16px] h-[16px]"
									:src="item.token.network.image != 'undefined' ? item.token.network.image : null" alt="network-img" />
							</div>

							<div>
								<h3>{{ item.token.token_ticker }}</h3>
								<div class="flex items-center gap-1">
									<h5 class="text-sm text-white/50 truncate max-w-[50px] 2lg:truncate-none 2lg:max-w-full">
										{{ item.token.token_name }}
									</h5>
								</div>
							</div>
						</div>
					</td>
					<td class="pl-5 whitespace-nowrap">{{ divideNumberUsingDecimals(item.price, USDT_DECIMALS).toString() }} USDT</td>
					<td class="pl-5">{{ item.quantity }}</td>
					<td class="pl-5">{{ dayjs(item.unlock_date).diff(dayjs(item.created_at), "month") + 1 === 12 ? "1 Year" :
						`${dayjs(item.unlock_date).diff(dayjs(item.created_at), "month") + 1} Months` }}</td>
					<td class="pl-5">
						<div class="flex justify-end">
							<button class="btn btn-outline btn-error btn-xs" @click="useSweetAlertStore().showConfirmAlert('Are you sure you want to cancel this order?', undefined, () => {
								cancel.mutate({ id: item.id }, {
									onSuccess: async () => {
										useSweetAlertStore().showAlert('Success', 'Cancel Success', 'success');
										await list.refetch()
									}, onError: () => {
										useSweetAlertStore().showAlert('Error', 'Cancel failed', 'error');
									}
								})
							}, 'Cancel')">Cancel</button>
						</div>
					</td>
				</tr>
				<tr v-else>
					<td colspan="5">
						<NoData :redirectButton="true" content="No Asset Found"></NoData>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { useQuery } from "@tanstack/vue-query";
import dayjs from "dayjs"
import { divideNumberUsingDecimals } from "~/utils/number"
import { USDT_DECIMALS } from "~/utils/const"
const web3Store = useWeb3Store()
const network = ref(web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol)

const list = useQuery({
	queryKey: computed(() => ["order", "list", unref(network)]),
	queryFn: () => {
		return api.apiCall("GET", "/order", {
			network: unref(network),
			take: 50,
		})
	},
	cacheTime: 0,
})

const orders = computed(() => list.data.value?.data.message.data.filter(item => item.status !== 'cancelled') || [])

const cancel = useOrderCancelMutation()

</script>
