<template>
  <div>
    <div class="h-[22px] w-[78px] bg-secondary rounded-md animate-pulse mb-2"></div>
  </div>
  <div class="flex flex-col lg:flex-row items-start lg:items-end gap-3 pb-8 border-b border-textbox-border">
    <div class="w-[330px] h-[56px] bg-secondary rounded-xl animate-pulse mb-3 md:mb-0"></div>
    <div class="w-[261px] h-6 bg-secondary rounded-md animate-pulse"></div>
  </div>
  <div class="mt-8 md:mt-24 flex justify-between items-center">
    <div class="h-[30px] md:h-10 w-[140px] bg-secondary rounded-md animate-pulse mb-2"></div>
    <div class="h-[30px] w-[148px] bg-secondary rounded-md animate-pulse mb-2 md:hidden"></div>
  </div>
  <div class="mt-10 flex gap-4">
    <div class="h-[30px] w-[318px] bg-secondary rounded-md animate-pulse mb-2"></div>
    <div class="h-[30px] w-[148px] bg-secondary rounded-md animate-pulse mb-2 hidden md:block"></div>
  </div>
  <div class="mb-6 md:mb-[80px]">
    <div class="py-6 pt-4 hidden lg:block">
      <div class="flex items-center gap-2 mb-4" v-for="i in new Array(3)">
        <div
          class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
        ></div>
        <div
          class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
        ></div>
        <div
          class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
        ></div>
      </div>
    </div>    
  </div>
  <div class="pb-8 border-b border-textbox-border hidden lg:block">
    <div class="h-[30px] w-[161px] bg-secondary rounded-md animate-pulse"></div>
  </div>
  <div class="py-6 pt-10 hidden lg:block">
    <div class="flex items-center gap-2 mb-4" v-for="i in new Array(3)">
      <div
        class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
      ></div>
      <div
        class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
      ></div>
      <div
        class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
      ></div>
    </div>
  </div> 
     
  <div class="lg:hidden">
    <div class="mb-[56px]">
      <div class="h-[142px] mb-4 bg-secondary rounded-lg animate-pulse" v-for="i in new Array(3)"></div>
    </div>
    <div class="h-[27px] w-[140px] mb-6 bg-secondary rounded-md animate-pulse"></div>
    <div class="h-[142px] mb-4 bg-secondary rounded-lg animate-pulse" v-for="i in new Array(3)"></div>
  </div>
</template>

<script setup>

</script>

<style>

</style>