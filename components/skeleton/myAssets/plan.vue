<template>
  <div>
    <div class="mb-8 flex justify-between">
      <div
        class="w-[50px] h-[26px] bg-secondary rounded-sm animate-pulse"
      ></div>
      <!-- <div
        class="w-[88px] h-[26px] bg-secondary rounded-sm animate-pulse"
      ></div> -->
    </div>
    <div class="mb-5 flex gap-5">
      <div class="w-1/2 h-[556px] bg-secondary rounded-xl animate-pulse"></div>
      <div class="w-1/2 h-[556px] bg-secondary rounded-xl animate-pulse"></div>
    </div>
    <div class="mb-20">
      <div class="w-full h-[533px] bg-secondary rounded-xl animate-pulse"></div>
    </div>
    <div>
      <div class="flex justify-between items-center pb-4">
        <div class="flex gap-4 text-2sm">
          <div
            v-for="i in new Array(2)"
            class="w-[50px] h-[21px] bg-secondary animate-pulse"
          ></div>
        </div>

        <div class="flex gap-8">
          <div
            class="w-[118px] h-[40px] bg-secondary animate-pulse rounded-lg"
          ></div>
          <div
            class="w-[220px] h-[40px] bg-secondary animate-pulse rounded-lg"
          ></div>
        </div>
      </div>

      <div class="py-4">
        <div class="flex items-center gap-2 mb-4" v-for="i in new Array(6)">
          <div
            class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
          ></div>
          <div
            class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>

          <div
            class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
