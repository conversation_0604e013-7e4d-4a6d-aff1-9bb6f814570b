<template>
  <div>
    <div class="mb-20 lg:mb-[120px] flex gap-5 mt-4">
      <div
        class="w-full h-[200px] lg:w-1/2 lg:h-[300px] bg-secondary rounded-xl animate-pulse"
      ></div>
      <div
        class="w-1/2 h-[300px] bg-secondary rounded-xl animate-pulse hidden lg:block"
      ></div>
    </div>

    <div class="mb-[56px] lg:mb-16">
      <!-- <h1 class="text-2xl font-satoshiMedium mb-4">Best Deals</h1> -->
      <div
        class="w-[112.69px] h-[36px] bg-secondary animate-pulse mb-6 lg:mb-8"
      ></div>

      <div class="lg:hidden">
        <div class="grid grid-cols-2 gap-8 w-[590px]">
          <div
            v-for="i in new Array(2)"
            class="bg-secondary w-[295px] h-[302px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>

      <div class="hidden lg:block">
        <div class="grid grid-cols-4 gap-5">
          <div
            v-for="i in new Array(4)"
            class="bg-secondary max-w-[295px] w-full h-[246px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
    </div>

    <div class="mb-14 lg:mb-[80px]">
      <!-- <h1 class="text-2xl font-satoshiMedium mb-4">Best Deals</h1> -->
      <div
        class="w-[112.69px] h-[36px] bg-secondary animate-pulse mb-6 lg:mb-8"
      ></div>

      <div class="lg:hidden">
        <div class="grid grid-cols-2 gap-8 w-[590px]">
          <div
            v-for="i in new Array(2)"
            class="bg-secondary w-[295px] h-[302px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
      
      <div class="hidden lg:block">
        <div class="grid grid-cols-4 gap-5">
          <div
            v-for="i in new Array(4)"
            class="bg-secondary max-w-[295px] w-full h-[246px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
    </div>

    <div class="mb-[56px] lg:mb-16">
      <!-- <h1 class="text-2xl font-satoshiMedium mb-4">Best Deals</h1> -->
      <div
        class="w-[112.69px] h-[36px] bg-secondary animate-pulse mb-6 lg:mb-8"
      ></div>

      <div class="lg:hidden">
        <div class="grid grid-cols-2 gap-8 w-[590px]">
          <div
            v-for="i in new Array(2)"
            class="bg-secondary w-[295px] h-[172px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
      <div class="hidden lg:block">
        <div class="grid grid-cols-4 gap-5">
          <div
            v-for="i in new Array(4)"
            class="bg-secondary max-w-[295px] w-full h-[178px] rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
    </div>

    <div>
      <div class="flex justify-between items-center pb-6 lg:pb-8">
        <div class="flex gap-10 lg:gap-4 text-2sm">
          <div
            v-for="i in new Array(3)"
            class="w-[60px] h-[21px] lg:w-[50px] bg-secondary animate-pulse"
          ></div>
        </div>

        <div
          class="w-8 h-8 lg:w-[150px] lg:h-[21px] bg-secondary animate-pulse"
        ></div>
      </div>

      <div class="py-6 pt-4 hidden lg:block">
        <div class="flex items-center gap-2 mb-4" v-for="i in new Array(6)">
          <div
            class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
          ></div>
          <div
            class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>

          <div
            class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>
        </div>
      </div>

      <div class="lg:hidden">
        <div class="flex items-center gap-2 mb-4" v-for="i in new Array(3)">
          <div
            class="w-full h-[142px] max-h-[142px] bg-secondary rounded-[12px] animate-pulse"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
