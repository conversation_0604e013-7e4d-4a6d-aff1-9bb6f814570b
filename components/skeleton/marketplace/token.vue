<template>
  <div class="hidden md:block">
    <div class="flex items-center justify-between mb-4">
      <div class="h-[32px] w-[92px] rounded-xl bg-secondary animate-pulse"></div>
      <div class="h-[32px] w-[92px] rounded-xl bg-secondary animate-pulse"></div>
    </div>
  </div>

  <div class="flex flex-col lg:flex-row gap-5 mb-[56px] md:mb-[64px]">
    <div class="lg:w-5/12">
      <div
        class="bg-secondary p-8 rounded-xl mb-5 h-[335px] md:h-[233px] animate-pulse"
      ></div>

      <div class="bg-secondary p-8 rounded-xl h-[60px] md:h-[325px] animate-pulse"></div>
    </div>
    <div class="lg:w-7/12 flex flex-col">
      <div
        class="bg-secondary p-8 rounded-xl mb-5 h-[60px] md:h-[404px] animate-pulse"
      ></div>

      <div class="bg-secondary p-8 rounded-xl h-[116px] md:h-[154px] animate-pulse"></div>
    </div>
  </div>

  <div>
    <div
      class="h-[24px] w-[120px] animate-pulse bg-secondary rounded-sm mb-8"
    ></div>
    <div>
      <div class="flex justify-between items-center pb-4">
        <div class="flex gap-4 text-2sm">
          <div
            v-for="i in new Array(2)"
            class="w-[50px] h-[21px] bg-secondary rounded-sm animate-pulse"
          ></div>
        </div>

        <div class="w-8 h-8 md:w-[150px] md:h-[21px] bg-secondary rounded-sm md:rounded-xl animate-pulse"></div>
      </div>

      <div class="py-4 hidden md:block">
        <div class="flex items-center gap-2 mb-4" v-for="i in new Array(3)">
          <div
            class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
          ></div>
          <div
            class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>

          <div
            class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>
        </div>
      </div>
      <div class="md:hidden">
        <div class="flex flex-col" v-for="i in new Array(3)">
          <div
            class="h-[142px] mb-4 bg-secondary rounded-xl animate-pulse"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
