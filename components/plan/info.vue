<template>
  <div
    class="bg-white h-[556px] bg-opacity-[6%] p-8 rounded-[12px] font-medium"
  >
    <div
      class="flex gap-5 pb-6 items-center border-b-2 border-textbox-border w-full"
    >
      <img
        width="64px"
        height="64px"
        :src="currentToken?.token_image"
        alt="token-logo"
      />
      <div class="flex flex-col">
        <h3 class="text-2xl text-white">
          {{ currentToken?.token_name }}
          <span class="font-satoshiBlack">
            {{
              numeral(divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS).toString()).format(
                "0,000.00"
              )
            }}
            USD</span
          >
        </h3>
        <div class="flex gap-3">
          <h5 class="text-2sm">
            LOT
            {{ lot.marketplace_display_id }}
          </h5>
          <a
            :href="`${useRuntimeConfig().public.nuxt_explorer_dev}${
              currentToken?.token_address
            }`"
            target="_blank"
          >
            <div class="flex gap-1">
              <h5 class="text-primary text-2sm">
                {{ currentToken?.token_ticker }}
              </h5>
              <inline-svg
                class="fill-primary w-4"
                :src="arrowRightUpIcon"
              ></inline-svg>
            </div>
          </a>
        </div>
      </div>
      <div>
        <h3></h3>
      </div>
    </div>
    <div class="mt-10 flex flex-col gap-7 text-2sm text-input-icons">
      <div class="w-full flex justify-between">
        <h5>Best Price</h5>
        <div class="flex items-center gap-1">
          <h5 class="text-white">
            <!-- {{
                lot?.best_price ?? lot?.listed_price
              }} -->
            {{
              formatToken(
                divideNumberUsingDecimals(lot?.listed_price, USDT_DECIMALS)
              )
            }}
          </h5>
          <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
        </div>
      </div>
      <div class="w-full flex justify-between">
        <h5>Lot Balance</h5>
        <h5 class="text-white">
          {{
            formatToken(
              divideNumberUsingDecimals(lot.remaining_listing, lot.decimal)
            )
          }}
        </h5>
      </div>
      <div class="w-full flex justify-between">
        <h5>Max Discount to Spot</h5>
        <h5 class="text-success">
          Up to
          {{ formatToken(lot?.discount_pct) }}%
        </h5>
      </div>
      <div class="w-full flex justify-between">
        <h5>Market Cap</h5>
        <h5 class="uppercase text-white">
          ${{
            numeral(divideNumberUsingDecimals(currentToken.market_cap, USDT_DECIMALS).toString()).format(
              "0,0a.00"
            )
          }}
        </h5>
      </div>
      <div class="w-full flex justify-between">
        <h5>Max Supply</h5>
        <h5 class="uppercase text-white">
          {{ common.zeroToNil(numeral(maxSupply).format("0,0a.00")) }}
        </h5>
      </div>
      <div class="w-full flex justify-between uppercase">
        <h5>FDV</h5>
        <h5 class="text-white">
          ${{ numeral(divideNumberUsingDecimals(lot.fdv, USDT_DECIMALS).toString()).format("0,0a.00") }}
        </h5>
      </div>
      <div class="w-full flex justify-between">
        <h5>Market Price</h5>
        <h5 class="text-white">
          {{
            common.zeroToNil(
              numeral(divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS).toString()).format(
                "0,0a.00"
              ),
              true
            )
          }}
          <span
            :class="`text-sm ${
              currentToken.twf_hour_changes * 1 > 0
                ? 'text-success'
                : 'text-error'
            }`"
            >{{ common.twfSymbol(currentToken.twf_hour_changes * 1 > 0)
            }}{{ currentToken.twf_hour_changes }}%
          </span>
        </h5>
      </div>
      <div class="w-full flex justify-between">
        <h5>First Unlock Begins</h5>
        <h5 class="text-white">
          {{
            $dayjs(lot?.start_date * 1)
              .add(lot?.cliff_duration, timeUnit)
              .format("DD/MM/YYYY")
          }}
          <span class="text-xs text-white/50">{{
            getCountDown(
              $dayjs(lot?.start_date * 1)
                .add(lot?.cliff_duration, timeUnit)
                .format("x") * 1
            )
          }}</span>
        </h5>
      </div>
    </div>
  </div>
</template>

<script setup>
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatToken } from "~/utils/number";
</script>

<style lang="scss" scoped></style>
