<script setup>
import "vue-awesome-paginate/dist/style.css";
import arrowDown from "~/assets/images_new/icons/paginate-arrow.svg";
import InlineSvg from "vue-inline-svg";

const props = defineProps({
  totalItems: Number,
});

const emit = defineEmits(["callback"]);

const onClickHandler = (page) => {
  console.log(page);
  emit("callback");
};

const currentPage = ref(1);
const itemsPerPage = 10;
const maxPagesShown = 10;

console.log("paginate props", props);

defineExpose({
  currentPage,
  itemsPerPage,
  maxPagesShown,
});
</script>

<template>
  <div class="flex justify-center mt-8">
    <vue-awesome-paginate
      :total-items="totalItems"
      :items-per-page="itemsPerPage"
      :max-pages-shown="maxPagesShown"
      v-model="currentPage"
      @click="onClickHandler"
    >

      <template #prev-button>
        <span>
          <inline-svg
            :src="arrowDown"
            class="rotate-90 w-3 h-3"
          ></inline-svg>
        </span>
      </template>

      <template #next-button>
        <span>
          <inline-svg
            :src="arrowDown"
            class="-rotate-90 w-3 h-3"
          ></inline-svg>
        </span>
      </template>
    </vue-awesome-paginate>
  </div>
</template>

<style>
.pagination-container {
  display: flex;
  column-gap: 12px;
}

.paginate-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7CD3F8;
  width: 16px;
  height: 16px;
  font-size: 12px;
  border-radius: 2px !important; 
}

.paginate-buttons:hover {
  @apply bg-primary rounded-[4px] text-primary bg-opacity-20;
}

.active-page {
  @apply bg-primary rounded-[4px] text-primary bg-opacity-20;
}


/* .active-page:hover {
  background-color: #2988c8;
} */
</style>
