<script setup>
const props = defineProps(['modelValue', 'inputProps', 'error', 'leftIcon', 'precision'])
const emit = defineEmits(['update:modalValue'])

const handleInput = ($event) => {
	const newValue = $event.target.value
	const SEPARATOR = '.'
	if (!newValue.includes(SEPARATOR) || newValue.split(SEPARATOR).at(-1).length <= Number(props.precision)){
		emit('update:modelValue', newValue)
	} else {
		$event.target.value = props.modelValue
	}
}

</script>

<template>
	<div class="flex flex-col gap-0.5 relative mb-1">
		<label
			class="input h-[48px] flex items-center gap-2 mb-4"
			:class="
				props.error
					? 'border input-error'
					: 'border border-textbox-border'
			"
		>
			<img
				v-if="props.leftIcon"
				class="w-[20px] h-[20px]"
				:src="props.leftIcon"
				alt="token-img"
			/>
			<input
				type="number"
				step="0.000001"
				class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0 focus:ring-offset-0"
				min="0"
				:value="props.modelValue"
				@input="handleInput"
				v-bind="props.inputProps"
			/>
		</label>
	</div>
</template>
