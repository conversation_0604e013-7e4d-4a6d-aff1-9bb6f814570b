<template>
  <Teleport to="body">
    <input type="checkbox" id="newListModal" class="modal-toggle" />
    <div
      :class="[
        isMobile
          ? isMounted
            ? 'translate-y-0 transform transition-all duration-500 ease-out'
            : 'translate-y-full transform transition-all duration-500 ease-out'
          : '',
        'md:translate-y-0',
      ]"
      role="dialog"
      class="modal bg-modal backdrop-blur-[12px] backdrop:bg-transparent font-medium"
    >
      <div
        v-if="lotData"
        class="modal-box h-fit max-h-[650px] min-w-full md:min-w-[460px] md:max-w-[460px] pt-8 px-5 pb-6 bg-modal md:rounded-[12px] fixed bottom-0 md:static transform transition-all duration-500 ease-out"
      >
        <div class="flex justify-between items-center mb-8">
          <h3 class="text-xl">Create Listing</h3>
          <div for="modal-action">
            <label
              for="newListModal"
              @click="handleClose"
              class="btn btn-xs btn-circle btn-ghost text-lg text-white font-bold hover:bg-transparent focus:outline-none"
              >✕</label
            >
          </div>
        </div>
        <form @submit.prevent="toSummary">
          <div class="flex flex-col gap-2">
            <div class="flex justify-between text-input-icons text-sm">
              <div class="flex items-center">
                <h6 class="text-white text-sm">Token Amount</h6>
              </div>

              <div class="flex flex-row items-center gap-2">
                <inline-svg
                  class="stroke-input-icons"
                  width="12"
                  height="12"
                  :src="walletIcon"
                ></inline-svg>
                <div>
                  Sell Limit:
                  <span class="text-white">
                    {{
                      formatToken(
                        getSellable(
                          divideNumberUsingDecimals(
                            lotData?.total_allocated ?? 0,
                            lotData?.token_decimal ?? 0
                          ).toString(),
                          divideNumberUsingDecimals(
                            lotData?.market_amount ?? 0,
                            lotData?.token_decimal ?? 0
                          ).toString(),
                          divideNumberUsingDecimals(
                            lotData?.claimed_amount ?? 0,
                            lotData?.token_decimal ?? 0
                          ).toString(),
                          divideNumberUsingDecimals(
                            lotData?.selling_limit ?? 0,
                            lotData?.token_decimal ?? 0
                          ).toString(),
                          divideNumberUsingDecimals(
                            lotData?.listed_amount ?? 0,
                            lotData?.token_decimal ?? 0
                          ).toString()
                        )
                      )
                    }}
                  </span>
                  <span class="mx-2 text-white/50">/</span>

                  <span class="text-white/50">
                    {{
                      formatToken(
                        divideNumberUsingDecimals(
                          lotData?.selling_limit ?? 0,
                          lotData?.token_decimal ?? 0
                        ).plus(
                          divideNumberUsingDecimals(
                            lotData?.listed_amount ?? 0,
                            lotData?.token_decimal ?? 0
                          )
                        )
                      )
                    }}</span
                  >
                </div>
              </div>
            </div>
            <div class="flex flex-col gap-0.5 relative mb-8">
              <label
                class="input h-[48px] flex items-center gap-2 mb-4 bg-modal-textbox"
                :class="
                  listForm.amount * 1 > sellLimit * 1
                    ? 'border input-error'
                    : 'border border-textbox-border'
                "
              >
                <img
                  :src="lotData.token_image"
                  alt="token-img"
                  class="w-[20px] h-[20px] rounded-full coin-border"
                />
                <input
                  type="number"
                  class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0 focus:ring-offset-0"
                  :step="TOKEN_STEP"
                  required
                  v-model="listForm.amount"
                  min="1"
                  @keypress="common.handleNumberInput($event, 6, 100)"
                  @input="common.handleNumberInput($event, 6, 100)"
                  onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode === 46"
                />
                <span
                  @click="maxInput"
                  class="text-primary text-sm cursor-pointer"
                  >MAX</span
                >
              </label>
              <div class="flex flex-col items-center mt-3 mb-1 relative">
                <!-- Range Slider -->
                <input
                  type="range"
                  v-model="sliderValue"
                  :min="minValue"
                  :max="maxValue"
                  step="0.0001"
                  @input="updatePercentage"
                  id="slider"
                  ref="slider"
                  class="slider relative z-10 w-full h-1 bg-range-grey rounded-lg appearance-none cursor-pointer"
                />
                <div class="w-full flex justify-between absolute -top-0.5 z-0">
                  <div class="h-2 w-2"></div>
                  <div
                    class="h-2 w-2 rounded-full"
                    :class="percentage > 25 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div
                    class="h-2 w-2 rounded-full"
                    :class="percentage > 50 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div
                    class="h-2 w-2 rounded-full"
                    :class="percentage > 75 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div class="h-2 w-2"></div>
                </div>
              </div>
              <div class="flex justify-between text-primary text-xs mt-1">
                <h5>Min {{ numeral(minValue).format("0,0", Math.floor) }}</h5>
                <h5>Max {{ numeral(maxValue).format("0,0", Math.floor) }}</h5>
              </div>
              <!-- <div>{{ sellLimit }}</div> -->
              <h3
                v-if="listForm.amount * 1 > sellLimit * 1"
                class="text-xs text-error absolute top-[50px]"
              >
                Not enough {{ lotData.token_ticker }}
              </h3>
            </div>
          </div>
          <div class="flex flex-col gap-2 text-sm mb-6 relative">
            <div class="flex justify-between text-input-icons">
              <div class="flex items-center">
                <inline-svg :src="inputIcon" class="rotate-180"></inline-svg>
                <h6 class="text-white text-sm">Price</h6>
              </div>
            </div>
            <label
              class="input input-bordered h-[48px] flex items-center gap-2 bg-modal-textbox border-textbox-border"
            >
              <img
                class="w-[20px] h-[20px] coin-border rounded-full"
                :src="usdtToken"
                alt="token-img"
              />
              <input
                type="number"
                class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0"
                :step="USDT_STEP"
                @keypress="common.handleNumberInput($event, 6, 100)"
                @input="common.handleNumberInput($event, 6, 100)"
                onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode === 46"
                required
                v-model="listForm.pricePerToken"
                max="1000000000"
              />
            </label>
            <div class="flex justify-between">
              <!-- <h3 class="text-xs font-medium" v-if="lotData?.token_price">
                1 {{ lotData.token_ticker }} =
                {{
                  common.formatUSDT(
                    web3Store.formatNumber(
                      lotData.token_price ?? 0,
                      lotData.token_decimal ?? 18
                    )
                  )
                }}
                USDT
              </h3> -->
              <h3
                class="text-xs font-medium text-error"
                v-if="
                  Number(listForm.amount) > 0 &&
                  Number(listForm.pricePerToken) > 0 &&
                  Number(listForm.amount) * Number(listForm.pricePerToken) < 1
                "
              >
                Minimum total amount : 1 USDT
              </h3>
            </div>
          </div>
          <div class="text-sm flex flex-col gap-7 mb-7 relative">
            <div class="flex justify-between z-30">
              <div class="flex items-center relative">
                <div>Fill Type</div>
                <div class="relative inline-block group pl-1 pr-2">
                  <inline-svg
                    :src="infoIcon"
                    class="stroke-input-icons w-3 h-3"
                  ></inline-svg>
                  <div
                    class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
                  >
                    <div
                      class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll"
                    >
                      <div class="flex flex-col gap-3">
                        <div class="flex flex-col gap-1">
                          <h5><b>Single Fill</b></h5>
                          <h5>
                            The order must be filled in one complete
                            transaction; partial fills are not allowed.
                          </h5>
                        </div>
                        <div class="flex flex-col gap-1">
                          <h5><b>Allow Partial Fill</b></h5>
                          <h5>
                            The purchase can be completed with multiple orders
                            until the full amount is filled.
                          </h5>
                        </div>
                      </div>
                    </div>

                    <!-- Triangle pointer -->
                    <div
                      class="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-modal-textbox border-l border-b border-textbox-border rotate-45"
                    ></div>
                      
                  </div>
                </div>
              </div>

              <div class="flex gap-2">
                <input
                  type="checkbox"
                  class="checkbox checkbox-primary h-4 w-4 !rounded-[2px]"
                  v-model="listForm.fillType"
                  :true-value="'0'"
                  :false-value="'1'"
                />
                <div>Allow Partial Fill</div>
              </div>
            </div>
            <div class="flex justify-between z-20">
              <div class="flex items-center">
                <div>Pool Type</div>
                <div class="relative inline-block group pl-1 pr-2">
                  <inline-svg
                    :src="infoIcon"
                    class="stroke-input-icons w-3 h-3"
                  ></inline-svg>
                  <div
                    class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
                  >
                    <div
                      class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll"
                    >
                      <div class="flex flex-col gap-3">
                        <h5>
                          <b>Private Lot: </b>
                          The listing is hidden from the public UI but remains
                          on-chain and accessible through direct interaction.
                        </h5>
                        <h5>
                          <b>Public Lot: </b>
                          The listing is visible to all users on the platform's
                          UI
                          <i
                            >Disclaimer: All listings are on-chain and not
                            completely private.</i
                          >
                        </h5>
                      </div>
                    </div>

                    <!-- Triangle pointer -->
                    <div
                      class="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-modal-textbox border-l border-b border-textbox-border rotate-45"
                    ></div>
                      
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <input
                  type="checkbox"
                  disabled
                  class="checkbox checkbox-primary h-4 w-4 !rounded-[2px]"
                />
                <div>Make Private</div>
              </div>
            </div>
            <div class="flex justify-between z-10">
              <div class="flex items-center">
                <label class="text-sm" for="price_per_token"
                  >Discount Type</label
                >
                <div class="relative inline-block group pl-1 pr-2">
                  <inline-svg
                    :src="infoIcon"
                    class="stroke-input-icons w-3 h-3"
                  ></inline-svg>
                  <div
                    class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
                  >
                    <div
                      class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll"
                    >
                      <div class="flex flex-col gap-3">
                        <h5>
                          <b>Linear Discount: </b>
                          Up to 5% discount based on the proportion of total
                          supply purchased. The more you buy, the larger the
                          discount.
                        </h5>
                        <h5>
                          <b>Fixed Discount: </b>
                          A fixed percentage discount (e.g., 5%) applied only to
                          a single fill lot, requiring the entire lot to be
                          purchased.
                        </h5>
                      </div>
                    </div>

                    <!-- Triangle pointer -->
                    <div
                      class="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-modal-textbox border-l border-b border-textbox-border rotate-45"
                    ></div>
                      
                  </div>
                </div>
              </div>

              <!-- 
              <select
                class="h-[20px] min-h-[20px] max-h-[20px] bg-modal text-white text-sm !rounded-[4px] text-left leading-[20px] py-0"
                v-model="listForm.discountType"
                required
              >
                <option value="0" >No Discount</option>
                <option value="1">Linear</option>
                <option value="2">Fixed</option>
              </select> -->

              <div
                class="dropdown dropdown-bottom dropdown-end"
                ref="discountDropdown"
              >
                <div
                  @click="discountOpen = !discountOpen"
                  tabindex="0"
                  role="button"
                  class="btn h-[20px] min-h-[20px] max-h-[20px] capitalize bg-modal hover:bg-modal text-white text-sm border-none pr-0"
                >
                  <div>{{ discountTypeText[listForm.discountType] }}</div>
                  <inline-svg :src="chevronIcon"></inline-svg>
                </div>
                <ul
                  v-show="discountOpen"
                  tabindex="0"
                  class="dropdown-content menu border bg-modal border-textbox-border rounded-[8px] text-sm z-[1] w-[125px] p-2 shadow text-white"
                >
                  <li @click.prevent="changeDiscountType(0)">
                    <a class="py-0.5">No Discount</a>
                  </li>
                  <li
                    @click.prevent="changeDiscountType(1)"
                    v-if="listForm.fillType == 0"
                  >
                    <a class="py-0.5">Linear</a>
                  </li>
                  <li @click.prevent="changeDiscountType(2)">
                    <a class="py-0.5">Fixed</a>
                  </li>
                </ul>
              </div>
            </div>
            <div>
              <div
                v-if="listForm.discountType != 0"
                class="flex justify-between"
              >
                <div>
                  {{
                    listForm.discountType == 1 ? "Maximum Discount" : "Discount"
                  }}
                </div>

                <div
                  :class="[
                    'flex justify-center items-center text-center pr-2 w-[70px] max-w-[70px] !rounded-[4px] bg-modal-textbox',
                    {
                      'border border-error':
                        (listForm.discountType == 1 &&
                          listForm.linearDiscountPct !== null &&
                          (Number(listForm.linearDiscountPct) <= 0 ||
                            Number(listForm.linearDiscountPct) > 100)) ||
                        (listForm.discountType == 2 &&
                          listForm.fixDiscountPct !== null &&
                          (Number(listForm.fixDiscountPct) <= 0 ||
                            Number(listForm.fixDiscountPct) > 100)),
                      'border border-textbox-border':
                        (listForm.discountType == 1 &&
                          listForm.linearDiscountPct === null) ||
                        (listForm.discountType == 2 &&
                          listForm.fixDiscountPct === null) ||
                        (listForm.discountType == 1 &&
                          Number(listForm.linearDiscountPct) > 0 &&
                          Number(listForm.linearDiscountPct) <= 100) ||
                        (listForm.discountType == 2 &&
                          Number(listForm.fixDiscountPct) > 0 &&
                          Number(listForm.fixDiscountPct) <= 100),
                    },
                  ]"
                >
                  <input
                    v-if="listForm.discountType == 1"
                    type="number"
                    class="input input-bordered min-h-6 max-h-6 bg-transparent border-none w-full text-sm px-2"
                    v-model="listForm.linearDiscountPct"
                    max="100"
                    min="1"
                    step="0.01"
                    @keypress="common.handleNumberInput($event, 2, 100)"
                    @input="common.handleNumberInput($event, 2, 100)"
                  />
                  <input
                    v-if="listForm.discountType == 2"
                    type="number"
                    class="input input-bordered min-h-6 max-h-6 bg-transparent border-none w-full text-sm px-2"
                    v-model="listForm.fixDiscountPct"
                    max="100"
                    min="1"
                    step="0.01"
                    @keypress="common.handleNumberInput($event, 2, 100)"
                    @input="common.handleNumberInput($event, 2, 100)"
                    @blur="
                      listForm.fixDiscountPct = parseFloat(
                        listForm.fixDiscountPct
                      ).toFixed(2)
                    "
                  />
                  <div>%</div>
                </div>
              </div>
              <!-- <div class="text-right text-xs text-error mt-2">Max is 100%</div> -->
            </div>
          </div>

          <div class="flex justify-center">
            <button
              class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 font-normal disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
              :disabled="
                (listForm.discountType == 1 &&
                  (!listForm.linearDiscountPct ||
                    listForm.linearDiscountPct === '' ||
                    isNaN(Number(listForm.linearDiscountPct)))) ||
                (listForm.discountType == 2 &&
                  (!listForm.fixDiscountPct ||
                    listForm.fixDiscountPct === '' ||
                    isNaN(Number(listForm.fixDiscountPct)))) ||
                Number(listForm.amount) <= 0 ||
                Number(listForm.pricePerToken) <= 0 ||
                Number(listForm.amount) > Number(sellLimit) ||
                (listForm.discountType == 1 &&
                  Number(listForm.linearDiscountPct) <= 0) ||
                (listForm.discountType == 1 &&
                  Number(listForm.linearDiscountPct) > 100) ||
                (listForm.discountType == 2 &&
                  Number(listForm.fixDiscountPct) <= 0) ||
                (listForm.discountType == 2 &&
                  Number(listForm.fixDiscountPct) > 100) ||
                Number(listForm.amount) * Number(listForm.pricePerToken) < 1
              "
            >
              Review Listing
            </button>
          </div>
        </form>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="buyModal"
        >Close</label
      >
    </div>
  </Teleport>
</template>

<script setup>
import backIcon from "~/assets/images/icons/arrow-left.svg";
import chevronIcon from "~/assets/images_new/icons/chevron-down.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import { useWeb3Store } from "../../stores/web3";
import infoIcon from "~/assets/images_new/icons/info.svg";
import { TOKEN_STEP, USDT_STEP } from "~/utils/const";
import { divideNumberUsingDecimals, formatToken } from "~/utils/number";

const step = ref(1);
const sellLimit = ref(null);
let discountOpen = ref(false);

const fillTypeText = {
  0: "partial fill",
  1: "single fill",
};

const discountTypeText = {
  0: "no discount",
  1: "linear",
  2: "fixed",
};

const listTypeText = {
  0: "public",
  1: "private",
};

const props = defineProps({
  usdtBalance: Object,
  vestingAddress: String,
  lotData: Object,
});

console.log('lotData', props.lotData);

//slider
const sliderValue = ref(0);
const minValue = ref(0);
const maxValue = ref(0);
const percentage = ref(0);
const slider = ref(null);

const listForm = reactive({
  amount: null,
  pricePerToken: null,
  fillType: "1",
  listType: "0",
  discountType: "2",
  fixDiscountPct: null,
  linearDiscountPct: null,
  maxWhitelist: 0,
});

const web3Store = useWeb3Store();

const emit = defineEmits(["callback", "openListSummary"]);

function toDiscount() {
  let discountType = {
    0: 3,
    1: 2,
    2: 2,
  };

  step.value = discountType[listForm.discountType];
}

function toSummary() {
  updateModel();
  console.log("list form", listForm);
  document.getElementById("newListModal").checked = false;
  closeModal();

  document.getElementById("newListSummaryModal").checked = true;
  emit("openListSummary");
}

function updateModel() {
  if (listForm.discountType == 1) {
    listForm.fixDiscountPct = 0;
  } else if (listForm.discountType == 2) {
    listForm.linearDiscountPct = 0;
  }
}

async function listAction() {
  document.getElementById("listModal").close();
  useSweetAlertStore().showLoadingAlert("Processing", "listing");

  try {
    await useWeb3Store().listLot(props.vestingAddress, listForm);

    step.value = 1;
    useSweetAlertStore().showAlert("Success", "List success", "success");

    emit("callback");
  } catch (error) {
    useSweetAlertStore().showAlert("error", "Transaction Declined", "error");

    step.value = 1;
  }
}

function toPreviousStep() {
  if (listForm.discountType == 0) {
    step.value = 1;
  } else {
    step.value -= 1;
  }
}

function getSellable(
  totalAllocated,
  marketAmount,
  claimedAmount,
  sellLimit,
  listedAmount
) {
  const claimable = totalAllocated * 1 + marketAmount * 1 - claimedAmount * 1;

  console.log("sellable amount", {
    amt_totalAllocated: totalAllocated,
    amt_marketAmount: marketAmount,
    amt_claimedAmount: claimedAmount,
    amt_sellLimit: sellLimit,
    amt_listedAmount: listedAmount,
    amt_claimable: claimable,
  });

  return claimable > sellLimit * 1
    ? sellLimit * 1 < 0
      ? 0
      : sellLimit
    : claimable;
}

const updatePercentage = () => {
  percentage.value = (sliderValue.value / maxValue.value) * 100;
  // buyForm.input = usdtBalance.value * percentage.value / 100 ;

  listForm.amount = sliderValue;

  document.getElementById(
    "slider"
  ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
};

const computedDiscountValue = computed(() => {
  return listForm.discountType == 2
    ? listForm.fixDiscountPct
    : listForm.linearDiscountPct;
});

function calcSellLimit() {
  sellLimit.value = numeral(
    getSellable(
      divideNumberUsingDecimals(
        props.lotData?.total_allocated ?? 0,
        props.lotData?.token_decimal ?? 0
      ).toString(),
      divideNumberUsingDecimals(
        props.lotData?.market_amount ?? 0,
        props.lotData?.token_decimal ?? 0
      ).toString(),
      divideNumberUsingDecimals(
        props.lotData?.claimed_amount ?? 0,
        props.lotData?.token_decimal ?? 0
      ).toString(),
      divideNumberUsingDecimals(
        props.lotData?.selling_limit ?? 0,
        props.lotData?.token_decimal ?? 0
      ).toString(),
      divideNumberUsingDecimals(
        props.lotData?.listed_amount ?? 0,
        props.lotData?.token_decimal ?? 0
      ).toString()
    )
  ).format("0.00", Math.floor);
}

function maxInput() {
  listForm.amount = sellLimit.value;
}

function changeDiscountType(discountType) {
  listForm.discountType = discountType.toString();
  discountOpen.value = false;
  if (discountType == 0) {
    listForm.linearDiscountPct = 0;
    listForm.fixDiscountPct = 0;
  }
}

function handleInputChange(event, model, maxValue) {
  const newValue = Number(event.target.value);
  console.log("test", validateDecimalPlaces(newValue));

  // Only check maximum if maxValue is provided
  if (maxValue !== undefined && newValue > maxValue) {
    if (model === "token") {
      listForm.amount = maxValue;
    } else if (model === "price") {
      listForm.pricePerToken = maxValue;
    }
    return;
  }

  if (!validateDecimalPlaces(newValue)) {
    const decimalPlaces = 6;
    const numString = newValue.toString();
    const decimalIndex = numString.indexOf(".");
    if (decimalIndex === -1) {
      if (model == "token") {
        listForm.amount = newValue;
      } else if (model == "price") {
        listForm.pricePerToken = newValue;
      }
    } else {
      if (model == "token") {
        listForm.amount = numString.slice(0, decimalIndex + decimalPlaces + 1);
      } else if (model == "price") {
        listForm.pricePerToken = numString.slice(
          0,
          decimalIndex + decimalPlaces + 1
        );
      }
    }
  }
}

// modal function
const isMounted = ref(false);
const isMobile = ref(false);

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

watch(
  () => props.lotData,
  (newValue) => {
    calcSellLimit();
    maxValue.value = numeral(
      getSellable(
        divideNumberUsingDecimals(
          newValue?.total_allocated ?? 0,
          newValue?.token_decimal ?? 0
        ).toString(),
        divideNumberUsingDecimals(
          newValue?.market_amount ?? 0,
          newValue?.token_decimal ?? 0
        ).toString(),
        divideNumberUsingDecimals(
          newValue?.claimed_amount ?? 0,
          newValue?.token_decimal ?? 0
        ).toString(),
        divideNumberUsingDecimals(
          newValue?.selling_limit ?? 0,
          newValue?.token_decimal ?? 0
        ).toString(),
        divideNumberUsingDecimals(
          newValue?.listed_amount ?? 0,
          newValue?.token_decimal ?? 0
        ).toString()
      )
    ).format("0.00", Math.floor);
  }
);

watch(
  () => listForm.amount,
  (newValue) => {
    sliderValue.value = newValue;

    if (!newValue || newValue == "") {
      sliderValue.value = 0;
    }

    percentage.value = (newValue / maxValue.value) * 100;
    document.getElementById(
      "slider"
    ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
  }
);

watch(
  () => listForm.pricePerToken,
  (newValue) => {
    if (newValue > 1000000000) {
      listForm.pricePerToken = 1000000000;
    }
  }
);

watch(
  () => listForm.linearDiscountPct,
  (newValue) => {
    if (newValue > 100) {
      listForm.linearDiscountPct = 100;
    }
  }
);

watch(
  () => listForm.fixDiscountPct,
  (newValue) => {
    if (newValue > 100) {
      listForm.fixDiscountPct = 100;
    }
  }
);

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
    document.body.style.overflow = "auto";
    document.body.style.position = "";
  }

  console.log("close modal executed isMobile", isMobile.value);
};

const handleClose = () => {
  resetForm();
  closeModal();
};

const resetForm = () => {
  listForm.amount = null;
  listForm.pricePerToken = null;
  listForm.fillType = "1";
  listForm.listType = "0";
  listForm.discountType = "2";
  listForm.fixDiscountPct = null;
  listForm.linearDiscountPct = null;
  listForm.maxWhitelist = 0;
};

defineExpose({
  listForm,
  handleClick,
});
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}
</style>
