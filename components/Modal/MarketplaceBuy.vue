<template>
  <Teleport to="body">
    <input type="checkbox" id="buyModal" class="modal-toggle" />
    <div
      :class="[
        isMobile
          ? isMounted
            ? 'translate-y-0 transform transition-all duration-500 ease-out'
            : 'translate-y-full transform transition-all duration-500 ease-out'
          : '',
        'md:translate-y-0',
      ]"
      role="dialog"
      class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent font-medium"
    >
      <div
        v-if="lot"
        class="modal-box min-w-full md:min-w-[460px] md:max-w-[460px] pt-8 px-5 pb-6 bg-modal md:rounded-[12px] fixed bottom-0 md:static transform transition-all duration-500 ease-out"
      >
        <div class="flex items-center justify-between mb-8">
          <h3 class="flex items-center gap-3 text-xl">
            Buy Lot
            {{
              common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)
            }}
            <span
              class="text-input-icons text-xs px-[6px] py-1 rounded-[4px] bg-white/[8%]"
            >
              {{ lot.listing_type == 1 ? "Single Fill" : "Partial Fill" }}
            </span>
          </h3>

          <!-- <form method="dialog">
                    <button 
                        class="btn btn-sm btn-circle btn-ghost text-xl -mr-2.5">✕</button>
                </form> -->
          <div for="modal-action">
            <label
              for="buyModal"
              @click="handleClose"
              class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none"
              >✕</label
            >
          </div>
        </div>
        <form @submit.prevent="buyConfirm">
          <div class="flex flex-col gap-2">
            <div class="flex justify-between text-sm text-input-icons">
              <div class="flex items-center">
                <inline-svg :src="inputIcon"></inline-svg>
                <h6 class="text-sm text-white">Input</h6>
              </div>

              <div class="flex flex-row items-center gap-2">
                <inline-svg
                  class="stroke-input-icons"
                  width="12"
                  height="12"
                  :src="walletIcon"
                ></inline-svg>
                <div>{{ formatUSDT(usdtBalance) }} USDT</div>
              </div>
            </div>
            <!-- <div 
                  class="flex flex-col gap-0.5 relative "
                  :class="
                      lot.discount_type == 1 ? 'min-h-[125px]': 
                      'mb-6'
                    "
                  > -->
            <div class="flex flex-col gap-0.5 relative mb-4">
              <label
                class="input h-[48px] flex items-center gap-2 mb-4"
                :class="
                  buyForm.input * 1 > usdtBalance * 1
                    ? 'border input-error'
                    : 'border border-textbox-border'
                "
              >
                <img
                  class="w-[20px] h-[20px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
                <input
                  type="number"
                  class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0 focus:ring-offset-0"
                  :step="USDT_STEP"
                  required
                  v-model="buyForm.input"
                  @input="(e) => handleInputChange(e, buyForm)"
                  @keypress="(e) => handleKeyPress(e, buyForm)"
                  min="1"
                  :readonly="lot.listing_type == 1"
                />
                <span
                  @click="maxInput"
                  class="text-sm cursor-pointer text-primary"
                  >MAX</span
                >
              </label>
              <h3
                v-if="buyForm.input * 1 > usdtBalance * 1"
                class="text-xs text-error absolute top-[50px]"
              >
                Not enough USDT
              </h3>
              <h3
                v-if="
                  (buyForm.input || buyForm.input == 0) && buyForm.input * 1 < 1
                "
                class="text-xs text-error absolute top-[50px]"
              >
                Minimum purchase : 1 USDT
              </h3>
              <div
                v-if="lot.discount_type == 1"
                class="relative flex flex-col items-center mt-3 mb-1"
              >
                <!-- Range Slider -->
                <input
                  type="range"
                  v-model="sliderValue"
                  :min="minValue"
                  :max="maxValue"
                  :step="TOKEN_STEP"
                  @input="updatePercentage"
                  id="slider"
                  ref="slider"
                  class="relative z-10 w-full h-1 rounded-lg appearance-none cursor-pointer slider bg-range-grey"
                />
                <div class="w-full flex justify-between absolute -top-0.5 z-0">
                  <div class="w-2 h-2"></div>
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="percentage > 25 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="percentage > 50 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="percentage > 75 ? 'bg-range-blue' : 'bg-range-grey'"
                  ></div>
                  <div class="w-2 h-2"></div>
                </div>
                <!-- Slider Value and Percentage -->
                <!-- <div class="text-center">
                      <p class="text-lg font-semibold">Slider Value: {{ sliderValue }}</p>
                      <p class="text-lg font-semibold">Percentage: {{ percentage }}%</p>
                    </div> -->
              </div>
              <div
                v-if="lot.discount_type == 1"
                class="flex justify-between mt-1 text-xs text-primary"
              >
                <h5>Min {{ numeral(minValue).format("0,0", Math.floor) }}%</h5>
                <h5>Max {{ numeral(maxValue).format("0,0", Math.floor) }}%</h5>
              </div>
            </div>
          </div>
          <div class="relative flex flex-col gap-2 mb-6 text-sm">
            <div class="flex justify-between text-input-icons">
              <div class="flex items-center">
                <inline-svg :src="inputIcon" class="rotate-180"></inline-svg>
                <h6 class="text-sm text-white">Output</h6>
              </div>
              <div class="flex flex-row items-center gap-2">
                <inline-svg
                  class="stroke-input-icons"
                  width="12"
                  height="12"
                  :src="walletIcon"
                ></inline-svg>
                <div>
                  {{
                    formatUSDT(
                      divideNumberUsingDecimals(
                        lot.remaining_listing,
                        lot.token_decimal
                      )
                    )
                  }}
                  {{ lot.token_ticker }}
                </div>
              </div>
            </div>
            <label
              class="input input-bordered h-[48px] flex items-center gap-2"
              :class="
                toBigNumber(buyForm.output).isGreaterThan(
                  divideNumberUsingDecimals(lot.remaining_listing, lot.token_decimal)
                )
                  ? 'border input-error'
                  : 'border border-textbox-border'
              "
            >
              <img
                :src="lot.token_image"
                alt="token-img"
                class="w-[20px] h-[20px] rounded-full coin-border"
              />
              <input
                type="number"
                class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0"
                :step="TOKEN_STEP"
                required
                v-model="buyForm.output"
                @input="(e) => handleOutputChange(e, buyForm)"
                @keypress="(e) => handleKeyPress(e, buyForm)"
                :readonly="lot.listing_type == 1"
              />
              <span
                @click="maxInput"
                class="text-sm cursor-pointer text-primary"
                >MAX</span
              >
            </label>

            <div class="flex justify-between">
              <h3 class="text-xs font-medium">
                1 {{ lot.token_ticker }} =
                {{
                  formatUSDT(
                    divideNumberUsingDecimals(lot.listed_price, USDT_DECIMALS)
                  )
                }}
                USDT
              </h3>
              <h3
                class="text-xs text-error"
                v-if="
                  toBigNumber(buyForm.output).isGreaterThan(
                    divideNumberUsingDecimals(lot.remaining_listing, lot.token_decimal)
                  )
                "
              >
                Not enough {{ lot.token_ticker }}
              </h3>
            </div>
          </div>
          <div
            class="flex flex-col bg-modal-textbox rounded-[8px] text-sm p-4 gap-3 mb-6 text-input-icons"
          >
            <!-- <div class="flex justify-between">
              <div class="flex items-center">
                <h6>Platform Fees</h6>
                <div class="relative inline-block pl-1 pr-2 group">
                  <inline-svg :src="infoIcon" class="w-3 h-3 stroke-input-icons"></inline-svg>
                  <div class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition 
                   bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg
                    ">
                    <div class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll">
                      A platform fee of {{ lot.buy_fee }}% is charged upon completion of purchase 
                      notional value and charged in the quote currence
                    </div>
                    
                    
                    <div class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"></div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-1">
                <div>
                  {{
                    numeral(buyForm.platformFee).format(
                      useRuntimeConfig().public.usdtDecimal,
                      Math.floor
                    )
                  }}
                </div>
                <img
                  class="w-[14px] h-[14px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
              </div>
            </div> -->
            <div
              v-if="lot.discount_type != 0"
              class="flex justify-between text-success"
            >
              <span class="flex items-center">
                <div>{{ discountType[lot.discount_type] }} Discount</div>
                <!-- <span v-if="lot.max_discount * 1 > 0"
                      >(-{{
                        numeral(buyForm.discountPct * 100).format(
                          "0,0.00",
                          Math.floor
                        )
                      }}%)  </span
                    > -->
                <div
                  v-if="lot.discountType == 1"
                  class="relative inline-block pl-1 pr-2 group"
                >
                  <inline-svg
                    :src="infoIcon"
                    class="w-3 h-3 stroke-input-icons"
                  ></inline-svg>
                  <div
                    class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
                  >
                    <div
                      class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll"
                    >
                      The linear discount allows you to receive up to
                      {{
                        formatUSDT(buyForm.discountDeduct)
                      }}% off, proportional to the amount of the total supply
                      you purchase. The more you buy, the greater the discount,
                      reaching the full 5% at 100% of the supply.
                    </div>

                    <!-- Triangle pointer -->
                    <div
                      class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"
                    ></div>
                      
                  </div>
                </div>
              </span>
              <span class="flex items-center gap-1 text-success">
                <div>
                  {{ buyForm.discountDeduct }}
                </div>
                <img
                  class="w-[14px] h-[14px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
              </span>
            </div>
            <div class="flex justify-between text-white">
              <div>Cost</div>
              <div class="flex items-center gap-1">
                <div>
                  {{
                    formatUSDT(
                      divideNumberUsingDecimals(
                        lot.listed_price,
                        USDT_DECIMALS
                      ).multipliedBy(buyForm.output)
                    )
                  }}

                  <!-- {{ web3Store.formatNumber(lot.listed_price) * buyForm.output }} -->
                </div>
                <img
                  class="w-[14px] h-[14px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
              </div>
            </div>
          </div>
          <div class="flex justify-center">
            <button
              class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 font-medium disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
              :disabled="
                buyForm.input * 1 > usdtBalance * 1 ||
                toBigNumber(buyForm.output).isGreaterThan(
                  divideNumberUsingDecimals(lot.remaining_listing, lot.token_decimal)
                ) ||
                buyForm.input == null ||
                buyForm.output == null ||
                buyForm.input < 1 ||
                buyForm.output <= 0
              "
            >
              Review Buy
            </button>
          </div>
          <!-- <new-modal-buy-summary
                :lot="props.lot"
                :buyForm="buyModal?.buyForm"
                :isMarketplaceBuy="true"
                ref="newBuySummaryModal"
              ></new-modal-buy-summary> -->
        </form>
      </div>
      <label
        v-if="!isMobile"
        @click="handleClose()"
        class="modal-backdrop"
        for="buyModal"
        >Close</label
      >
    </div>
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import common from "~/utils/common";
import { USDT_DECIMALS, TOKEN_STEP, USDT_STEP } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber } from "~/utils/number";

const web3Store = useWeb3Store();
const emit = defineEmits(["openBuySummary"]);

const props = defineProps({
  lot: Object,
});

watch(props, (newValue) => {
  console.log("marketplace buy modal props", newValue);

  maxValue.value =
    newValue.lot.discount_pct == undefined
      ? newValue.lot.max_discount
      : newValue.lot.discount_pct;
});

const buyForm = reactive({
  input: null,
  output: null,
  discountDeduct: 0,
  discountPct: 0,
  platformFee: 0,
  received: 0,
  fullAmount: 0,
  payAmount: 0,
});

const discountType = {
  1: "Linear",
  2: "Fixed",
};

const usdtBalance = ref(0);

//slider
const sliderValue = ref(0);
const minValue = ref(0);
const maxValue = ref(30);
const percentage = ref(0);
const slider = ref(null);

web3Store.$subscribe(async (mutation, state) => {
  console.log("mutation", mutation, state);
  console.log("web3store isLogin", state.isLogin);

  if (state.isLogin) {
    console.log(
      "should not tirgger when false web3store isLogin",
      state.isLogin
    );
    const config = web3Store.getConfigByCurrentNetwork()
    usdtBalance.value = await web3Store.getTokenBalance(
      config.usdtToken,
      config.network.nativeCurrency.symbol
    );
  }
});

const discountPct = computed(() => {
  return props.lot?.max_discount !== undefined
    ? props.lot?.max_discount / 100
    : props.lot?.discount_pct / 100;
});
console.log("discountPct", discountPct.value);

function validateDecimalPlaces(value) {
  const regex = /^\d+(\.\d{0,6})?$/; // Regex to allow up to 6 decimal places
  return regex.test(value);
}

function handleInputChange(event, model) {
  // Add input validation

  const newValue = Number(event.target.value);
  console.log("test", validateDecimalPlaces(newValue));

  if (model && !validateDecimalPlaces(newValue)) {
    const decimalPlaces = 6;
    const numString = newValue.toString();
    const decimalIndex = numString.indexOf(".");
    if (decimalIndex === -1) {
      model.input = newValue;
    } else {
      model.input = numString.slice(0, decimalIndex + decimalPlaces + 1);
    }
  }

  const bestPrice = divideNumberUsingDecimals(
    props.lot.listed_price, 
    USDT_DECIMALS
  ).toNumber();

  console.log("bestPrice", bestPrice);
  const totalListing = divideNumberUsingDecimals(
    props.lot.total_listing,
    props.lot.token_decimal
  ).toNumber();
  // const discountPct =
  //   props.lot.max_discount !== undefined
  //     ? props.lot.max_discount / 100
  //     : props.lot.discount_pct / 100;

  buyForm.fullAmount = roundToPrecision(toBigNumber(newValue).dividedBy(bestPrice).toNumber());
  buyForm.received = roundToPrecision(toBigNumber(newValue).dividedBy(bestPrice).toNumber());
  buyForm.output = buyForm.received;

  if (props.lot.discount_type == 1) {
    const discount = (buyForm.fullAmount / totalListing) * discountPct.value;
    buyForm.discountDeduct = roundToPrecision(toBigNumber(newValue).multipliedBy(discount).toNumber());
    buyForm.discountPct = roundToPrecision(discount);
  } else if (props.lot.discount_type == 2) {
    buyForm.discountDeduct = roundToPrecision(toBigNumber(newValue).multipliedBy(discountPct.value).toNumber());
    buyForm.discountPct = newValue == 0 ? 0 : discountPct.value;
  } else {
    buyForm.discountDeduct = 0;
    buyForm.discountPct = 0;
  }

  const buyerFee =
    props.lot.buy_fee !== undefined
      ? props.lot.buy_fee / 100
      : props.lot.buyer_fee / 100;

  buyForm.platformFee = roundToPrecision(
    toBigNumber(buyForm.input).minus(buyForm.discountDeduct).multipliedBy(buyerFee).toNumber()
  );
  buyForm.payAmount = roundToPrecision(
    toBigNumber(buyForm.input).minus(buyForm.discountDeduct).plus(buyForm.platformFee).toNumber()
  );
}

function handleOutputChange(event) {
  const newValue = Number(event.target.value);

  console.log("handleOutputChange called with:", props.lot);
  const bestPrice = divideNumberUsingDecimals(
    props.lot.listed_price,
    USDT_DECIMALS
  ).toNumber();

  percentage.value =
    toBigNumber(buyForm.output).dividedBy(
      divideNumberUsingDecimals(
        props.lot.remaining_listing,
        props.lot.token_decimal
      )
    ).multipliedBy(100).toNumber();
  sliderValue.value = (percentage.value * maxValue.value) / 100;

  buyForm.input = roundToPrecision(toBigNumber(newValue).multipliedBy(bestPrice).toNumber());

  console.log("buyForm.input", buyForm.input);
  handleInputChange({ target: { value: buyForm.input } });

  if (slider.value) {
    document.getElementById(
      "slider"
    ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
  }
}

function roundToPrecision(value, precision = 6) {
  return parseFloat(value.toFixed(precision));
}

function updateOutput(lot) {
  console.log("update output executed", lot);
  if (lot.listing_type == 1) {
    buyForm.output = divideNumberUsingDecimals(
      lot.remaining_listing,
      lot.token_decimal
    ).toString();
    maxValue.value =
      (lot.remaining_listing / lot.total_listing) * maxValue.value;
    handleOutputChange({ target: { value: buyForm.output } });
  } else {
    buyForm.input = null;
    buyForm.output = null;
  }
}

function maxInput() {
  if (props.lot.listing_type == 1) {
    return;
  }

  if (
    toBigNumber(usdtBalance.value).dividedBy(
      divideNumberUsingDecimals(props.lot.listed_price, USDT_DECIMALS)
    ).isGreaterThan(
      divideNumberUsingDecimals(props.lot.remaining_listing, props.lot.token_decimal)
    )
  ) {
    buyForm.output = divideNumberUsingDecimals(
      props.lot.remaining_listing,
      props.lot.token_decimal
    ).toString();
    handleOutputChange({ target: { value: buyForm.output } });
  } else {
    buyForm.input = numeral(usdtBalance.value).format("0.0000", Math.floor);
    handleInputChange({ target: { value: buyForm.input } });
  }
}

async function buyConfirm() {
  // const tokenBalance = await web3Store.getTokenBalance(
  //   useRuntimeConfig().public.usdtAddress
  // );

  const tokenBalance = usdtBalance.value;

  console.log("buy usdt balance", tokenBalance);


  console.log("buyForm.payAmount", buyForm.payAmount);
  if (Number(buyForm.payAmount) > Number(tokenBalance)) {
    document.getElementById("buyModal").checked = false;
    closeModal();

    useSweetAlertStore().showAlert("Error", "Insufficient Token", "error");
    return;
  } else if (
    toBigNumber(buyForm.output).isGreaterThan(
      divideNumberUsingDecimals(props.lot.remaining_listing, props.lot.token_decimal)
    )
  ) {
    document.getElementById("buyModal").checked = false;
    closeModal();

    useSweetAlertStore().showAlert("Error", "Exceed maximum amount", "error");
    return;
  }
  document.getElementById("buyModal").checked = false;
  closeModal();

  document.getElementById("newBuySummaryModal").checked = true;
  emit("openBuySummary");
}

const updatePercentage = () => {
  percentage.value = (sliderValue.value / maxValue.value) * 100;
  // buyForm.input = usdtBalance.value * percentage.value / 100 ;

  buyForm.output =
    divideNumberUsingDecimals(
      props.lot.remaining_listing,
      props.lot.token_decimal
    ).multipliedBy(percentage.value).dividedBy(100).toString();

  handleOutputChange({ target: { value: buyForm.output } });

  document.getElementById(
    "slider"
  ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
};

// modal function
const isMounted = ref(false);
const isMobile = ref(false);

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

onMounted(() => {
  if (web3Store.isInit) {
    const config = web3Store.getConfigByCurrentNetwork()
    web3Store
      .getTokenBalance(
        config.usdtToken, 
        config.network.nativeCurrency.symbol
      ).then((bal) => {
        usdtBalance.value = bal;
      });
  }
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

const resetForm = () => {
  // Reset buyForm values
  Object.assign(buyForm, {
    input: null,
    output: null,
    discountDeduct: 0,
    discountPct: 0,
    platformFee: 0,
    received: 0,
    fullAmount: 0,
    payAmount: 0,
  });

  // Reset slider values
  sliderValue.value = 0;
  percentage.value = 0;
  if (slider.value) {
    const sliderElement = document.getElementById("slider");
    if (sliderElement) {
      sliderElement.value = 0;
      sliderElement.style.background = "#2B2D3D";
    }
  }
};

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
    document.body.style.overflow = "auto";
    document.body.style.position = "";
  }
  console.log("close modal executed isMobile", isMobile.value);
};

const handleClose = () => {
  closeModal();
  // resetForm();
};

// Add the keypress event handler
function handleKeyPress(event, model) {
  common.handleNumberInput(event, 6);
}

defineExpose({
  updateOutput,
  buyForm,
  handleClick,
  resetForm,
});
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}
</style>
