<template>
  <Teleport to="body">
    <input type="checkbox" id="newBuySummaryModal" class="modal-toggle" />
    <div
      :class="[
        isMobile ? (isMounted ? 'translate-y-0 transform transition-all duration-500 ease-out' : 'translate-y-full transform transition-all duration-500 ease-out') : '',
        'md:translate-y-0'
      ]"
      role="dialog"
      class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent"
    >
      <div
        v-if="lot && buyForm"
        class="modal-box font-medium min-w-full md:min-w-[460px] md:max-w-[460px] md:max-h-[605px] px-5 pt-8 pb-6 bg-modal rounded-[12px]
        fixed bottom-0 md:static transform transition-all duration-300 ease-out"
      >
        <div class="flex justify-between items-center mb-8 md:mb-10 md:h-[22px]">
          <h3 class="text-xl leading-5">Order Confirmation</h3>
          <div for="modal-action">
            <label
              for="newBuySummaryModal"
              @click="handleClose" 
              class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none"
              >✕</label
            >
          </div>
        </div>
        <form @submit.prevent="buyConfirm" class="font-medium">
          <div class="flex flex-col items-center justify-center mb-8">
            <img
              :src="tokenImage"
              alt="token-img"
              class="w-[64px] h-[64px] rounded-full mb-3 coin-border"
            />
            <h5 class="text-sm font-normal leading-4 opacity-60">Token Amount</h5>
            <h3 class="text-[28px] font-satoshiBlack leading-10">
              {{ formatUSDT(buyForm.received) }}
            </h3>
            <h5 class="text-xs leading-3 text-white opacity-60">
              {{ lot.token_ticker }} Price
              {{
                formatUSDT(divideNumberUsingDecimals(props.tokenPrice, USDT_DECIMALS))
              }}
              USDT
            </h5>
          </div>
          <div
            class="bg-modal-textbox rounded-[8px] text-sm text-input-icons p-5 font-medium mb-4 md:mb-6 leading-4"
          >
            <div class="flex justify-between mb-3 md:mb-4">
              <div>Price</div>
              <div class="flex items-center gap-1">
                <h6>
                  {{
                    formatUSDT(
                      divideNumberUsingDecimals(
                        lot.listed_price,
                        USDT_DECIMALS
                      )
                    )
                  }}
                </h6>
                <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
              </div>
            </div>
            <div class="flex justify-between">
              <div>Subtotal</div>
              <div class="flex items-center gap-1">
                <h6>
                  {{
                    formatUSDT(
                      toBigNumber(buyForm.payAmount).plus(buyForm.discountDeduct).minus(buyForm.platformFee)
                    )
                  }}
                </h6>
                <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
              </div>
            </div>
            <hr class="border-0.5 border-textbox-border my-3 md:my-5" />
            <div class="flex flex-col gap-3 mb-4 md:gap-4">
              <div
                class="flex justify-between text-sm font-medium text-white/60"
              >
                <div class="flex items-center text-input-icons">
                  <h6>Platform Fees</h6>
                  <div class="relative inline-block pl-1 pr-2 group">
                    <div class="">
                      <inline-svg :src="infoIcon" class="w-3 h-3 stroke-input-icons"></inline-svg>
                    </div>
                    <div class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition 
                    bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg
                      ">
                      <div class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll">
                        A platform fee of {{ formatToken(lot.buy_fee ?? lot.buyer_fee) }}% is charged upon completion of purchase 
                        notional value and charged in the quote currence
                      </div>
            
                      <!-- Triangle pointer -->
                      <div class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"></div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-1">
                  <div>
                    {{
                      formatUSDT(buyForm.platformFee)
                    }}
                  </div>
                  <img
                    class="w-[14px] h-[14px]"
                    :src="usdtToken"
                    alt="token-img"
                  />
                </div>
              </div>
              <div class="flex justify-between text-sm text-success">
                <div>
                  Discount (-{{
                    formatToken(buyForm.discountPct * 100)
                  }}%)
                </div>
                <div class="flex items-center gap-1">
                  <div>
                    <!-- {{
                      common.formatUSDT(buyForm.discountDeduct)
                    }} -->

                    {{ buyForm.discountDeduct }}
                  </div>
                  <img
                    class="w-[14px] h-[14px]"
                    :src="usdtToken"
                    alt="token-img"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between text-white text-2sm">
              <div class="flex items-center">
                <h6>Total Amount</h6>
                <div class="relative inline-block pl-1 pr-2 group">
                  <inline-svg :src="infoIcon" class="w-3 h-3 stroke-white"></inline-svg>
                  <div class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition 
                    bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg
                    ">
                    <div class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll">
                      <div class="flex flex-col gap-3">
                        <div>The amount of <b>{{ lot.token_ticker }}</b> the user will pay after applying the discount.</div>
                        <div><b>Formula:</b></div>
                        <div><b>Total Amount = (Price × Quantity) × (1 - Discount Percentage)</b></div>
                      </div>
                    </div>
                    
                    
                    <!-- Triangle pointer -->
                    <div class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"></div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-1">
                <h6>
                  {{ 
                    formatUSDT(buyForm.payAmount)
                  }}
                </h6>
                <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
              </div>
            </div>
          </div>

          <div class="flex justify-center mb-4">
            <button
              class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 font-medium"
              :disabled="isCalculatingNetworkFee"
            >
              <span v-if="isCalculatingNetworkFee" class="loading loading-spinner loading-md" />
              <span v-else>Buy Now</span>
            </button>
          </div>
          <div
            class="flex items-center justify-center gap-1 font-medium"
          >
            <inline-svg :src="gasIcon"></inline-svg>
            <h5 class="text-sm leading-3 text-input-icons">Est. Network Fee: 
              <span v-if="isCalculatingNetworkFee">Calculating...</span>
              <span v-else-if="networkFee && networkFee > 0">{{ numeral(divideNumberUsingDecimals(networkFee, 18).toString()).format("0.[00000000]", Math.round()) }} {{ lot.network_symbol }}</span>
              <span v-else-if="networkFee == -1n">Failed to load.</span>        
            </h5>
          </div>
        </form>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="newBuySummaryModal">Close</label>
    </div>

    <ModalApprove
      @emitApprove="buyConfirm"
      :lot="props.lot"
      :isApprove="isApprove"
      :status="approveStatus"
      ref="newApproveModal"
      id="newApproveModal"
    >
    </ModalApprove>
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import gasIcon from "~/assets/images_new/icons/gas.svg";
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber, formatToken } from "~/utils/number";

const props = defineProps({
  lot: Object,
  buyForm: Object,
  isMarketplaceBuy: Boolean,
  tokenImage: Object,
  tokenPrice: Object,
});

const summaryBuyForm = ref();
const newApproveModal = ref(null);

const { buyForm } = toRefs(props);

console.log("props", props);

const web3Store = useWeb3Store();

const route = useRoute();
const router = useRouter();
let isApprove = ref(null);
let approveStatus = ref(null);

watch(
  props,
  (newProps, oldProps) => {
    console.log('buy summary props',newProps, oldProps);
    summaryBuyForm.value = newProps.buyForm;
  },
  { deep: true }
);

async function buyConfirm() {
  approveStatus.value = "pending";
  document.getElementById("newBuySummaryModal").checked = false;
  closeModal();

  // check token balance
  const config = web3Store.getConfigByCurrentNetwork()
  const tokenBalance = await web3Store.getTokenBalance(
    config.usdtToken,
    config.network.nativeCurrency.symbol
  );

  if (Number(buyForm.value.payAmount) > Number(tokenBalance)) {
    useSweetAlertStore().showAlert("Error", "Insufficient Token", "error");
    return;
  } else if (
    toBigNumber(buyForm.value.output).isGreaterThan(
      divideNumberUsingDecimals(props.lot.remaining_listing, props.lot.token_decimal)
    )
  ) {
    useSweetAlertStore().showAlert("Error", "Exceed maximum amount", "error");
    return;
  }

  const buyParams = {
    vestingPlan: props.lot.plan_contract_address,
    listingId: props.lot.list_id,
    amount: buyForm.value.output,
    referral: props.isMarketplaceBuy ? route.query.referral : null,
    tokenDecimal : props.lot.token_decimal
  };

  console.log("route ", route);
  console.log("buyParams summary page", buyParams);

  isApprove.value = await web3Store.checkAllowance(
    web3Store.getConfigByCurrentNetwork().usdtToken,
    web3Store.getConfigByCurrentNetwork().marketplaceContract
  );

  console.log("buy lot", props.lot);
  console.log("buy lot params", buyForm.value);
  console.log("is approve", isApprove);
  console.log("is approve", approveStatus);
  if (!isApprove.value) {
    // useSweetAlertStore().showLoadingAlert("Processing", "Approving allowance");
    newApproveModal.value.handleClick();
    document.getElementById("newApproveModal").checked = true;
    try {
      await useWeb3Store().approveAllowance(
        web3Store.getConfigByCurrentNetwork().usdtToken,
        web3Store.getConfigByCurrentNetwork().marketplaceContract
      );
      isApprove.value = true;
      newApproveModal.value.handleClick();
      document.getElementById("newApproveModal").checked = true;
      buy(buyParams);
    } catch (error) {
      newApproveModal.value.handleClick();
      document.getElementById("newApproveModal").checked = true;
      approveStatus.value = "reject";
      //useSweetAlertStore().showAlert("error", error, "error");
      return;
    }
  }
  newApproveModal.value.handleClick();
  document.getElementById("newApproveModal").checked = true;
  buy(buyParams);
}

async function buy(buyParams) {
  try {
    await web3Store.buyLot(buyParams);
    console.log("buy parameter", buyParams);
    approveStatus.value = "confirm";
    console.log("approve states", approveStatus);
    document.getElementById("newApproveModal").checked = true;
    setTimeout(() => {
      newApproveModal.value.closeModal();
      document.getElementById("newApproveModal").checked = false;
      useSweetAlertStore().showBuySuccessAlert(
        "All Done!",
        "Buy Success",
        "success"
      );
    }, 3000);
  } catch (error) {
    console.error(error);
    approveStatus.value = "reject";
    document.getElementById("newApproveModal").checked = true;
    useSweetAlertStore().showAlert("error", "Transaction Declined", "error");
  }
}

// modal function
const isMounted = ref(false);
const isMobile = ref(false);
const networkFee = ref(null)
const isAttemptedToCalculateNetworkFee = ref(false)
const isCalculatingNetworkFee = ref(false)


watch(isMounted, async () => {
  if (isMounted && !isAttemptedToCalculateNetworkFee.value) {
    const buyParams = {
      vestingPlan: props.lot.plan_contract_address,
      listingId: props.lot.list_id,
      amount: buyForm.value.output,
      referral: props.isMarketplaceBuy ? route.query.referral : null,
      tokenDecimal : props.lot.token_decimal
    };
    try {
      isCalculatingNetworkFee.value = true
      networkFee.value = await web3Store.getBuyLotNetworkFee(buyParams)
    } catch (err) {
      console.error('err', err)
    } finally {
      isCalculatingNetworkFee.value = false
      isAttemptedToCalculateNetworkFee.value = true
    }
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = 'hidden';
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = 'auto';
  } else {
    isMounted.value = false;
  }
};

const handleClose = () => {
  closeModal();
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

defineExpose({
  handleClick,
});
</script>

<style>

</style>
