<template>
  <dialog id="newSuccessModal" class="modal bg-secondary/5 backdrop-blur-sm moda-show font-satoshiRegular"> 
    <div
      class="modal-box w-[380px] min-h-[340px] max-h-[346px] p-5 py-7 bg-modal rounded-[12px]"
    >
      <form method="dialog">
          <button 
              class="btn btn-sm btn-circle btn-ghost absolute right-3 top-5">✕</button>
      </form>
      <div class="flex flex-col justify-center items-center mt-6">
        <div class="mb-4">
          <inline-svg
            class="w-[60px] h-[60px]"
            :src="checkCircleIcon"
          ></inline-svg>
        </div>
        <h3 class="text-xl mb-4">All Done!</h3>
        <h6
          class="font-satoshiLight text-2sm mb-6"
        >Lorem ipsum dolor sit amet, consectetur adip is 
        </h6>
        <nuxt-link to=" /myassets">
          class="btn btn-primary w-full min-h-[40px] max-h-[40px] text-2sm mb-5"
        >
          View in My Assets
        </nuxt-link>
        <div
           class="text-primary text-sm"
        >
          Back to Market
        </div>
      </div>
    </div>
  </dialog>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import checkCircleIcon from "~/assets/images_new/icons/check-circle.svg";


</script>

<style scoped>

</style>