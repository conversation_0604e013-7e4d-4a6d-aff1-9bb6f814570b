<template>
  <Teleport to="body">
    <input type="checkbox" id="planDetailsModal" class="modal-toggle" />
    <div
      :class="[
        isMobile
          ? isMounted
            ? 'translate-y-0 transform transition-all duration-500 ease-out'
            : 'translate-y-full transform transition-all duration-500 ease-out'
          : '',
        'md:translate-y-0',
      ]"
      role="dialog"
      class="modal bg-modal backdrop-blur-[12px] backdrop:bg-transparent"
    >
      <div
        v-if="selectedPlan"
        class="modal-box font-medium w-full md:w-[460px] px-5 pt-8 pb-6 bg-modal rounded-[12px] fixed bottom-0 md:static transform transition-all duration-300 ease-out"
      >
        <div class="flex justify-end items-center h-6">
          <div for="modal-action">
            <label
              for="planDetailsModal"
              @click="handleClose"
              class="btn btn-xs btn-circle btn-ghost text-white text-lg hover:bg-transparent focus:outline-none h-full"
              >✕</label
            >
          </div>
        </div>
        <div>
          <div class="flex flex-col justify-center items-center mb-8">
            <img :src="selectedPlan.token_image" class="w-16 h-16 mb-4 rounded-full" />
            <h5 class="text-white text-xl">{{ selectedPlan.display_id }}</h5>
            <div class="flex flex-col items-center">
              <h5 class="text-2sm text-input-icons">{{ selectedPlan.plan_name }}</h5>

              <nuxt-link
                :href="`${useRuntimeConfig().public.explorerUrl}/address/${
                  selectedPlan.vesting_address
                }`"
                target="_blank"
                class="flex gap-1 items-center"
              >
                <h5 class="text-sm text-primary">
                  {{ common.formatAddress(selectedPlan.vesting_address ?? "-") }}
                </h5>
                <inline-svg
                  :src="arrowRightUpIcon"
                  class="w-[14px] h-[14px] fill-primary"
                ></inline-svg>
              </nuxt-link>
            </div>
          </div>
          <div class="w-full flex gap-3 mb-3">
            <div
              class="bg-modal-textbox w-1/2 flex flex-col gap-2 items-center py-4 px-5 text-2sm rounded-[8px] leading-5"
            >
              <h5 class="text-input-icons">Cliff Start</h5>
              <h5 class="text-white text-center">
                {{ common.formatDateTimeV4(selectedPlan.start) }}
              </h5>
            </div>
            <div
              class="bg-modal-textbox w-1/2 flex flex-col gap-2 items-center py-4 px-5 text-2sm rounded-[8px] leading-5"
            >
              <h5 class="text-input-icons">Cliff End</h5>
              <h5 class="text-white text-center">
                {{ common.formatDateTimeV4(cliffEndDate) }}
              </h5>
            </div>
          </div>
          <div
            class="flex flex-col gap-5 bg-modal-textbox text-2sm p-5 rounded-[8px] mb-6 leading-5"
          >
            <div class="flex justify-between">
              <h5 class="text-input-icons">Type</h5>
              <h5 class="text-white">{{ selectedPlan.function }}</h5>
            </div>
            <div class="flex justify-between">
              <h5 class="text-input-icons">Unlock Start Date</h5>
              <h5 class="text-white">{{ common.formatDateTimeV4(firstDropDate) }}</h5>
            </div>
            <div class="flex justify-between">
              <h5 class="text-input-icons">Vesting End</h5>
              <h5 class="text-white">{{ common.formatDateTimeV4(selectedPlan.end) }}</h5>
            </div>
          </div>
          <div class="text-2sm text-white flex flex-col gap-5 mb-6">
            <div class="flex justify-between">
              <h5>Allocated Amount</h5>
              <div class="flex gap-1 items-center">
                <div>
                  {{
                    numeral(
                      divideNumberUsingDecimals(
                        selectedPlan.total_allocated ?? 0,
                        selectedPlan.token_decimal ?? 0
                      ).toString()
                    ).format("0,000.00")
                  }}
                </div>
                <img :src="selectedPlan.token_image" class="w-5 h-5 rounded-full" />
              </div>
            </div>
            <div class="flex justify-between">
              <h5>Listed Amount</h5>
              <div class="flex gap-1 items-center">
                <div>
                  {{
                    numeral(
                      divideNumberUsingDecimals(
                        selectedPlan.listed_amount ?? 0,
                        selectedPlan.token_decimal ?? 0
                      ).toString()
                    ).format("0,000.00")
                  }}
                </div>
                <img :src="selectedPlan.token_image" class="w-5 h-5 rounded-full" />
              </div>
            </div>
          </div>
        </div>
        <button
          @click="goToMarketplace"
          class="btn btn-primary min-h-10 max-h-10 w-full cursor-pointer hover:opacity-80"
        >
          Go To Market
        </button>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="planDetailsModal">Close</label>
    </div>
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import gasIcon from "~/assets/images_new/icons/gas.svg";
import arrowDownIcon from "~/assets/images_new/icons/chevron-down.svg";
import backIcon from "~/assets/images_new/icons/chevron-left.svg";
import logoBlue from "~/assets/images/common/logo_blue_1_5x.webp";
import arrowRightUpIcon from "~/assets/images/icons/arrow-right-up.svg";

import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import utc from "dayjs/plugin/utc";
import { TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals } from "~/utils/number";

const props = defineProps({
  selectedPlan: Object,
});

const router = useRouter();
const timeUnit = TIME_UNIT;
const startDate = ref(null);
const endDate = ref(null);
const cliffEndDate = ref(null);
const firstDropDate = ref(null);

function calcCliff() {
  startDate.value = dayjs(props.selectedPlan.start * 1);
  endDate.value = dayjs(props.selectedPlan.end * 1);

  console.log("startDate", startDate.value);
  console.log("endDate", endDate.value);
  console.log("cliff duration", props.selectedPlan.cliff_duration);

  cliffEndDate.value = startDate.value.add(props.selectedPlan.cliff_duration, timeUnit);

  firstDropDate.value = startDate.value.add(props.selectedPlan.cliff_duration, timeUnit);

  if (props.selectedPlan.function === "CYCLE") {
    firstDropDate.value = startDate.value
      .add(props.selectedPlan.cliff_duration, timeUnit)
      .add(props.selectedPlan.duration, timeUnit);
  }

  // Calculate end time for cycle-based drops
  // if (props.selectedPlan.function === "CYCLE") {
  //   cliffEndDate.value = startDate.value.add(
  //     props.selectedPlan.duration * props.selectedPlan.cycle + props.selectedPlan.cliff_duration,
  //     timeUnit
  //   );
  // }
}

function goToMarketplace() {
  router.push("/");
}

// modal function
const isMounted = ref(false);
const isMobile = ref(false);

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
  }
};

const handleClose = () => {
  closeModal();
};

onMounted(async () => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

watch(props, (newValue, oldValue) => {
  calcCliff();
});

defineExpose({
  handleClick,
});
</script>

<style></style>
