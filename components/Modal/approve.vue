<template>
  <input type="checkbox" id="newApproveModal" class="modal-toggle" />
  <div 
    :class="[
        isMobile ? (isMounted ? 'translate-y-0 transform transition-all duration-500 ease-out' 
        : 'translate-y-full transform transition-all duration-500 ease-out') : '',
        'md:translate-y-0'
      ]"
    class="modal bg-secondary/5 backdrop-blur-sm " 
    role="dialog"
  >
    <div
      v-if="lot"
      class="modal-box min-w-full md:min-w-[460px] md:max-w-[460px] min-h-[391px] max-h-[391px] md:min-h-[383px] md:max-h-[383px] px-5 py-7 md:pb-6 
      bg-modal rounded-[20px] md:rounded-[12px]
      fixed bottom-0 md:static transform transition-all duration-500 ease-out"
    >
      <div class="flex justify-between items-center mb-8 md:mb-[22px]">
        <h3 class="text-xl font-medium">Buy Lot 
          {{
              common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)
            }}
        </h3>
        <div for="modal-action relative">
          <label
          @click="handleClose"
            for="newApproveModal"
            class="btn btn-xs btn-circle btn-ghost text-lg hover:bg-transparent focus:outline-none absolute top-6 right-4"
            >✕</label
          >
        </div>
      </div>
      <div
        class="flex flex-col justify-center items-center gap-1 h-[99px] mb-7 md:mb-[30px]"
      >
        <div>
          <div class="loading-animation"></div>
        </div>
        <h6 class="text-sm text-[#535561] font-medium">Loading</h6>
      </div>

      <div
        class="text-2sm bg-modal-textbox rounded-[8px] mb-6 h-[102px] p-5"
      >
        <div class="flex flex-col gap-[22px] relative font-medium">
          <div class="flex gap-[14px] items-center">
            <div
              class="w-[14px] h-[14px] rounded-full flex items-center justify-center"
              :class="{
                'bg-[#0C4CAC]': isApprove === false && status === 'pending',
                'bg-error': isApprove === false && status === 'reject',
                'bg-success': isApprove === true,
              }"
            >
              <div
                v-if="isApprove === false && status === 'pending'"
                class="w-[6px] h-[6px] rounded-full bg-white"
              ></div>
              <inline-svg
                v-else-if="isApprove === false && status === 'reject'"
                :src="crossIcon"
                class="w-[10px]"
              ></inline-svg>
              <inline-svg
                v-else-if="isApprove === true"
                :src="checkIcon"
                class="w-[10px]"
              ></inline-svg>
            </div>
            <h5>Approve Allowance</h5>
          </div>
          <div class="flex gap-[14px] items-center">
            <div
              class="w-[14px] h-[14px] rounded-full flex items-center justify-center"
              :class="{
                'bg-[#0C4CAC] opacity-[30%]':
                  isApprove === false ||
                  (isApprove === true && status == 'pending'),
                'bg-error': isApprove === true && status == 'reject',
                'bg-success': isApprove === true && status == 'confirm',
              }"
            >
              <div
                v-if="
                  isApprove === false ||
                  (isApprove === true && status == 'pending')
                "
                class="w-[6px] h-[6px] rounded-full bg-white"
              ></div>
              <inline-svg
                v-else-if="isApprove === true && status == 'reject'"
                :src="crossIcon"
                class="w-[10px]"
              ></inline-svg>
              <inline-svg
                v-else-if="isApprove === true && status == 'confirm'"
                :src="checkIcon"
                class="w-[10px]"
              ></inline-svg>
            </div>
            <h5>Confirm & Buy</h5>
          </div>
          <div class="absolute left-[6px] top-[21px]">
            <inline-svg :src="lineIcon" class="min-h-[21px]"></inline-svg>
          </div>
        </div>
      </div>
      <div
        class="flex justify-center items-center text-sm hover:underline hover:cursor-pointer font-medium"
      >
        <h6 @click="$emit('emitApprove')">Proceed in your wallet</h6>
      </div>
    </div>
    <label v-if="!isMobile" class="modal-backdrop h-screen" for="newApproveModal">Close</label>
    <new-modal-success ref="newSuccessModal"></new-modal-success>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import lineIcon from "~/assets/images_new/step/step-line.svg";
import checkIcon from "~/assets/images_new/step/check.svg";
import crossIcon from "~/assets/images_new/step/cross.svg";
const web3Store = useWeb3Store();

const props = defineProps({
  lot: Object,
  isApprove: Boolean,
  status: String,
});

// const status = ref('pending_approve');

// watch(status, (newValue, oldValue) => {
//   if (newValue === 'confirm') {
//     setTimeout(() => {
//       console.log("approve modal closed");
//       showConfirmModal();
//     }, 3000);
//   }
// });

watch(
  props,
  (newProps, oldProps) => {
    console.log(newProps, oldProps);
  },
  { deep: true }
);

async function showConfirmModal() {
  document.getElementById("newApproveModal").close();
  document.getElementById("newSuccessModal").showModal();
}

// modal function
const isMounted = ref(false);
const isMobile = ref(false);

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = 'hidden';
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = 'auto';
  } else {
    isMounted.value = false;
  }
};

const handleClose = () => {
  closeModal();
};

defineExpose({
  handleClick,
  closeModal,
});
</script>

<style scoped>
.steps-vertical .step {
  min-height: 42px;
  max-height: 42px;
  position: relative;
}

/* step line */
.steps .step-primary + .step-primary:before {
  background-color: transparent;
  /* border-left: 2px dashed #343849; */
  margin-left: 23.5px;
  padding-top: 10px;
  position: absolute;
}

/* step dot */
.steps .step-primary:after {
  background-color: #0c4cac;
  color: white;
  height: 14px;
  width: 14px;
}

/* step error line */
.steps .step-error + .step-primary:before {
  background-color: transparent;
  border-left: 2px dashed #343849;
  margin-left: 23.5px;
  position: absolute;
}

/* step error line */
.steps .step-success + .step-error:before {
  background-color: transparent;
  border-left: 2px dashed #343849;
  margin-left: 23.5px;
  position: absolute;
}

/* step success line */
.steps .step-success + .step-primary:before {
  background-color: transparent;
  border-left: 2px dashed #343849;
  margin-left: 23.5px;
  position: absolute;
}

/* step error dot */
.steps .step-error:after {
  color: white;
  height: 14px;
  width: 14px;
  position: relative;
}

/* step success line */
.steps .step-success + .step-success:before {
  background-color: transparent;
  border-left: 2px dashed #343849;
  margin-left: 23.5px;
  margin-top: 10px;
  position: absolute;
}

/* step success dot */
.steps .step-success:after {
  color: #090c1e;
  height: 14px;
  width: 14px;
  position: relative;
}
</style>
