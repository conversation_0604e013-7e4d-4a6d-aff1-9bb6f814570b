<template>
  <table class="w-full border-separate border-spacing-y-8">
    <thead class="text-sm font-medium text-input-icons">
      <tr>
        <td class="w-[20%]">Token</td>
        <td class="w-[15%]">Plan</td>
        <td class="w-[15%]">
          <div class="flex items-center">
            <h5>Amount</h5>
            <inline-svg :src="sortIcon" class="fill-input-icons"></inline-svg>
          </div>
        </td>
        <td class="w-[15%]">Claimable</td>
        <td class="w-[15%]">Next Unlock</td>
        <td class="w-[15%]">Sell Limit</td>
        <td class="w-[5%]"></td>
      </tr>
    </thead>
    <tbody class="text-2sm">
      <tr v-if="!tokenPlans || tokenPlans.length == 0">
        <td colspan="7">
          <NoData :redirectButton="true" content="No Asset Found"></NoData>
        </td>
      </tr>

      <TableMyAssetsToken
        @claimEvent="handleClaimEvent"
        @listEvent="handleListEvent"
        @summaryEvent="handleSummaryEvent"
        v-else
        v-for="token in tokenPlans"
        :token="token"
        :claimableAmounts="claimableAmounts"
      ></TableMyAssetsToken>
    </tbody>
  </table>
</template>

<script setup>

const props = defineProps({
  tokenPlans: Array,
  claimableAmounts: Object,
  showClaimEvent: Function,
  showListEvent: Function,
});

const web3Store = useWeb3Store()

const emit = defineEmits(["showClaimEvent", "showListEvent"]);

function handleClaimEvent(data) {
  console.log("handleClaimEvent", data);
  emit("showClaimEvent", data);
}

async function handleListEvent(data) {
  console.log("handleListEvent", data);
  await web3Store.switchNetwork(data.plan.network_symbol)
  emit("showListEvent", data);
}

function handleSummaryEvent(data) {
  console.log("handleSummaryEvent", data);
  emit("showSummaryEvent", data);
}
</script>

<style lang="scss" scoped></style>
