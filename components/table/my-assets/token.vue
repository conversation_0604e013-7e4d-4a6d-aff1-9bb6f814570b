<template>
  <tr>
    <td>
      <div class="flex gap-3">
        <div class="relative w-[44px] h-[44px] flex-shrink-0">
          <img class="w-full h-full rounded-full coin-border" :src="token.token_image" alt="token-img" />

          <img class="absolute bottom-0 w-[16px] h-[16px]" :src="token.network_image" alt="network-img" />
        </div>
        <div>
          <h4>{{ token.token_ticker }}</h4>
          <h5 class="text-sm text-input-icons">{{ token.token_name }}</h5>
        </div>
      </div>
    </td>
    <td>
      <div class="dropdown" ref="planDropdown">
        <div @click="planOpen = !planOpen" tabindex="0" role="button" class="flex items-center gap-4">
          <div>
            <h4 class="truncate max-w-[100px]">
              {{ token.plans[selectedIndex].plan_name }}
            </h4>
            <!-- <h5 class="text-sm text-input-icons">
              {{ token.plans[selectedIndex].display_id }}
            </h5> -->
          </div>
          <inline-svg class="w-[14px] h-[14px]" :src="dropdownIcon" :class="planOpen ? 'rotate-180' : ''" />
        </div>
        <ul v-show="planOpen" tabindex="0"
          class="dropdown-content py-4 max-h-[350px] overflow-y-auto bg-base-100 rounded-[8px] z-[1] w-[200px] border border-textbox-border shadow-[0_0_4px_0_#353333] text-white"
          @click.stop>
          <li v-for="(plan, index) in token.plans" :key="plan.display_id" @click="updateSelectedIndex(index)"
            class="transition-colors duration-200 cursor-pointer hover:bg-white/10">
            <a class="pl-3 flex justify-between items-center h-[50px]">
              <div>
                <h4 class="truncate max-w-[150px]">{{ plan.plan_name }}</h4>
                <h5 class="text-input-icons text-sm truncate max-w-[150px]">
                  {{ plan.display_id }}
                </h5>
              </div>
            </a>
          </li>
        </ul>
      </div>
    </td>
    <td>
      <span>
        {{
          formatToken(
            divideNumberUsingDecimals(token.plans[selectedIndex].raw_amount, token.token_decimal)
          )
        }}
      </span>
      <br />
      <span class="text-sm text-white/50">
        ≈
        {{
          formatUSDT(
            divideNumberUsingDecimals(token.token_price, USDT_DECIMALS).multipliedBy(
              divideNumberUsingDecimals(token.plans[selectedIndex].raw_amount, token.token_decimal)
            )
          )
        }}
        USDT
      </span>
    </td>

    <td v-if="Object.keys(claimableAmounts).length == 0">
      <div class="flex justify-center">
        <span class="loading loading-spinner loading-sm"></span>
      </div>
    </td>
    <td v-else>
      <span class="text-success">{{
        formatUSDT(
          claimableAmounts[token.plans[selectedIndex].vesting_id]?.tokenAmount || 0
        )
      }}
      </span>
      <br />
      <span class="text-sm text-white/50">
        ≈
        {{
          formatUSDT(
            claimableAmounts[token.plans[selectedIndex].vesting_id]
              ?.usdtValue || 0
          )
        }}
        USDT
      </span>
    </td>

    <td>
      {{ calcNextUnlock(token.plans[selectedIndex]) }}
    </td>
    <td>
      <h5>
        {{
          numeral(
            getSellable(
              divideNumberUsingDecimals(
                token.plans[selectedIndex].total_allocated ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                token.plans[selectedIndex].market_amount ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                token.plans[selectedIndex].claimed_amount ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                token.plans[selectedIndex].selling_limit ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                token.plans[selectedIndex].listed_amount ?? 0,
                token.token_decimal
              ).toString()
            )
          ).format("0,0.00")
        }}
      </h5>
    </td>
    <td>
      <div class="flex items-center gap-2">
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm"
          :disabled="claimableAmounts[token.plans[selectedIndex].vesting_id]?.tokenAmount * 1 == 0" @click="
            $emit('claimEvent', {
              plan: {
                ...token.plans[selectedIndex],
                token_image: token.token_image,
              },
            })
            ">
          Claim
        </button>
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm" :disabled="getSellable(
          divideNumberUsingDecimals(
            token.plans[selectedIndex].total_allocated ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            token.plans[selectedIndex].market_amount ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            token.plans[selectedIndex].claimed_amount ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            token.plans[selectedIndex].selling_limit ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            token.plans[selectedIndex].listed_amount ?? 0,
            token.token_decimal
          ).toString()
        ) <= 0" @click="
          $emit('listEvent', {
            plan: {
              ...token.plans[selectedIndex],
              token_image: token.token_image,
              token_ticker: token.token_ticker,
              token_price: token.price,
              token_decimal: token.token_decimal,
              network_symbol: token.network_symbol,
            },
          })
          ">
          List
        </button>
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm" @click="
          $emit('summaryEvent', {
            plan: {
              ...token.plans[selectedIndex],
              token_image: token.token_image,
              token_ticker: token.token_ticker,
              token_price: token.price,
              token_decimal: token.token_decimal,
            },
          })
          ">
          Summary
        </button>
      </div>
    </td>
  </tr>
</template>

<script setup>
import numeral from "numeral";
import optionsIcon from "~/assets/images_new/icons/options.svg";
import dropdownIcon from "~/assets/images_new/icons/paginate-arrow.svg";
import InlineSvg from "vue-inline-svg";
import { TIME_UNIT, USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, formatToken } from "~/utils/number";

const { $dayjs } = useNuxtApp();
const timeUnit = TIME_UNIT;

let planOpen = ref(false);

const props = defineProps({
  token: Object,
  claimableAmounts: {
    type: Object,
    default: null,
  },
  showClaimEvent: Function,
  showListEvent: Function,
});

const selectedIndex = ref(0);

function updateSelectedIndex(index) {
  selectedIndex.value = index;
  const elem = document.activeElement;
  if (elem) {
    elem?.blur();
  }

  planOpen.value = !planOpen.value;
}

function toggleDropdown() {
  const dropdownOpen = ref(false);
}

function calcNextUnlock(plan) {
  console.log("unlock plan", plan);
  const currentTime = $dayjs.utc();

  if (plan.function == "TIME") {
    if (
      $dayjs
        .utc(Number(plan.start))
        .add(plan.cliff_duration, timeUnit)
        .isAfter(currentTime)
    ) {
      return $dayjs
        .utc(Number(plan.start))
        .add(plan.cliff_duration, timeUnit)
        .format("YYYY-MM-DD");
    } else {
      return "Time-based";
    }
  } else {
    if ($dayjs.utc(Number(plan.start)).isAfter(currentTime)) {
      return $dayjs.utc(Number(plan.start)).format("YYYY-MM-DD");
    } else if ($dayjs.utc(Number(plan.end)).isBefore(currentTime)) {
      return "---";
    } else {
      // Calculate cycle duration and completed cycles
      const cycleDuration = plan.duration; // duration in days
      const startDate = $dayjs.utc(Number(plan.start));
      const daysSinceStart = currentTime.diff(startDate, timeUnit);
      const completedCycles = Math.floor(daysSinceStart / cycleDuration);

      console.log("completedCycles", completedCycles);

      // Calculate next unlock time
      const nextUnlockTime = startDate.add(
        (completedCycles + 1) * cycleDuration,
        timeUnit
      );

      // Check if next unlock is after end date
      if (nextUnlockTime.isAfter($dayjs.utc(Number(plan.end)))) {
        return "---";
      }

      return nextUnlockTime.format("YYYY-MM-DD");
    }
  }
}

function getSellable(
  totalAllocated,
  marketAmount,
  claimedAmount,
  sellLimit,
  listedAmount
) {
  const claimable = totalAllocated * 1 + marketAmount * 1 - claimedAmount * 1;

  console.log(
    "sellable amount",
    totalAllocated,
    marketAmount,
    claimedAmount,
    sellLimit,
    listedAmount,
    claimable
  );

  console.log({
    "claimable amount": claimable,
    "sell limit": sellLimit,
    "listed amount": listedAmount,
    "result 1": sellLimit * 1 - listedAmount * 1,
    "result 2": claimable,
  });

  console.log(
    "final return",
    claimable > sellLimit * 1 ? sellLimit * 1 : claimable
  );

  return claimable > sellLimit * 1 ? sellLimit * 1 : claimable;
}
</script>

<style lang="scss" scoped></style>
