<template>
	<button @click="handleOpenShareTokenModal">
		<slot></slot>
	</button>
	<Modal title="Share" :open="openedShareTokenModal" @on-close="handleCloseShareTokenModal">
		<div class="flex flex-col gap-4">
			<img :src="previewUrl" />
			<div class="flex justify-center gap-4 p-4">
				<a
					:href="`https://twitter.com/intent/tweet?text=${encodeURI(messageTwitter)}&hashtags=${encodeURI(hashtags.join(','))}`"
					class="bg-white/10 rounded-full flex items-center justify-center w-[30px] h-[30px]"
					target="_blank" 
				>
					<inline-svg
						class="w-[16px] h-[16px] fill-white"
						:src="xIcon"
					/>
				</a>
				<a 
					:href="`https://t.me/share/url?url=${encodeURI(link)}&text=${messageTelegram}`" 
					class="bg-white/10 rounded-full flex items-center justify-center w-[30px] h-[30px]"
					target="_blank" 
				>
					<inline-svg
						class="w-[18px] h-[18px] fill-white"
						:src="telegramIcon"
					/>
				</a>
				<button 
					@click="copyLink" 
					class="bg-white/10 rounded-full flex items-center justify-center w-[30px] h-[30px]"
				>
					<img class="w-[18px] h-[18px] fill-white" :src="isCopied ? checkCircleIcon : copyIcon" />
				</button>
			</div>
		</div>
	</Modal>
</template>

<script setup>
import copyIcon from "~/assets/images/icons/copy.svg";
import checkCircleIcon from "~/assets/images_new/icons/check-circle.svg";
import InlineSvg from "vue-inline-svg";
import xIcon from "~/assets/images_new/icons/social_media/x.svg";
import telegramIcon from "~/assets/images_new/icons/social_media/telegram.svg";

const props = defineProps({
	previewUrl: String,
	tokenName: String,
})

// current link
const link = typeof window === "object" ? window.location.href : undefined
const messageTwitter = `I just bid to buy ${props.tokenName} at a discount on @secondswap_io!\n\nYou can trade like a VC too: ${link}\n\n`
const hashtags = ["DeFi", "CryptoTrading", "LockedTokens", "tradelikeaVc"]
const messageTelegram = `${encodeURI(`\nCreate Bids to Buy ${props.tokenName} on  @SecondSwap  at a Discount! trade like a VC with @secondswap_community\n`)}${hashtags.map(hashtag => `%23${hashtag}`).join(' ')}`

const isCopied = ref(false);

const openedShareTokenModal = ref(false)

const handleOpenShareTokenModal = () => {
	openedShareTokenModal.value = true
}
const handleCloseShareTokenModal = () => {
	openedShareTokenModal.value = false
}

const copyLink = async () => {
	try {
		await navigator.clipboard.writeText(link)
		isCopied.value = true;
		setTimeout(() => {
			isCopied.value = false;
		}, 2000);
	} catch {
		console.error()
	}
}
</script>

<style scoped></style>
