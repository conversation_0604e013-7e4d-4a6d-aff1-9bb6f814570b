<template>
	<div class="flex my-6 overflow-auto border-b border-textbox-border">
		<div v-for="network in networks" :key="network.id" class="px-4 pb-3 cursor-pointer" :class="{
			'border-b-2 border-primary text-white font-medium': modelValue === network.symbol,
			'text-input-icons': modelValue !== network.id
		}" @click="emit('update:modelValue', network.symbol)">
			<div class="flex items-center gap-2">
				<img :src="network.image" class="w-5 h-5" />
				<span>{{ network.name }}</span>
			</div>
		</div>
	</div>
</template>

<script setup>
const props = defineProps(['modelValue'])
const emit = defineEmits(['update:modelValue'])
const networkList = useNetworkList()
const networks = computed(() => networkList.data.value?.data.message || [])
</script>
