<template>
  <div
    v-if="lot"
    id="buyComponent"
    class="bg-white bg-opacity-[6%] md:min-h-[533px] lg:min-h-[537px] px-5 py-6 md:px-5 md:py-6 lg:p-8 rounded-[12px] font-medium"
  >
    <div class="flex items-center justify-between mb-7">
      <h3 class="text-xl md:text-base">
        Buy LOT
        {{ common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id) }}
      </h3>
      <div
        class="bg-white text-input-icons bg-opacity-[8%] text-2sm p-3 py-[3px] rounded-[4px] font-medium"
      >
        {{ common.listType[lot.listing_type] }} Fill
      </div>
    </div>
    <form @submit.prevent="buyConfirm">
      <div class="flex flex-col gap-2">
        <div class="flex justify-between text-sm text-input-icons">
          <div class="flex items-center">
            <inline-svg
              :src="inputIcon"
              class="w-[14px] h-[14px] md:w-4"
            ></inline-svg>
            <h6 class="text-sm text-white md:text-2sm">Input</h6>
          </div>

          <div class="flex flex-row items-center gap-2">
            <inline-svg
              class="stroke-input-icons"
              width="12"
              height="12"
              :src="walletIcon"
            ></inline-svg>
            <div>
              {{ formatUSDT(props.usdtBalance) }}
              USDT
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-0.5 relative mb-7">
          <label
            class="input h-[48px] flex items-center gap-2 mb-5"
            :class="
              buyForm.input > props.usdtBalance
                ? 'border input-error'
                : 'border border-textbox-border'
            "
          >
            <img
              class="w-[20px] h-[20px] rounded-full coin-border"
              :src="usdtToken"
              alt="token-img"
            />
            <input
              type="number"
              class="w-full grow read-only:opacity-50"
              :step="USDT_STEP"
              required
              v-model="buyForm.input"
              min="1"
              :readonly="lot.listing_type == 1"
              @input="handleInputChange($event.target.value)"
              @keypress="(e) => common.handleNumberInput(e, 6)"
              :disabled="loadingInput !== null || loadingPercentage !== null"
            />
            <span @click="maxInput" class="text-sm cursor-pointer text-primary"
              >MAX</span
            >
          </label>
          <h3
            v-if="buyForm.input > props.usdtBalance"
            class="text-sm text-error absolute top-[50px]"
          >
            Not enough USDT
          </h3>

          <div
            v-if="lot.listing_type != 1"
            class="relative flex flex-col items-center mt-3 mb-1"
          >
            <!-- Range Slider -->
            <input
              :disabled="lot.listing_type == 1"
              type="range"
              v-model="sliderValue"
              :min="minValue"
              :max="maxValue"
              step="0.0001"
              @input="updatePercentage"
              id="slider"
              ref="slider"
              class="relative z-10 w-full h-1 rounded-lg appearance-none cursor-pointer slider bg-range-grey"
            />
            <div class="w-full flex justify-between absolute -top-0.5 z-0">
              <div class="w-2 h-2"></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 25 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 50 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 75 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div class="w-2 h-2"></div>
            </div>
            <!-- Slider Value and Percentage -->
            <!-- <div class="text-center">
                  <p class="text-lg font-semibold">Slider Value: {{ sliderValue }}</p>
                  <p class="text-lg font-semibold">Percentage: {{ percentage }}%</p>
                </div> -->
          </div>
          <!-- <div
            v-if="lot.discount_type == 0"
            class="relative flex flex-col items-center mt-3 mb-1"
          >
            <input
              type="range"
              disabled
              value="0"
              class="relative z-10 w-full h-1 rounded-lg appearance-none cursor-pointer bg-range-grey"
            />
            <div class="w-full flex justify-between absolute -top-0.5 z-0">
              <div class="w-2 h-2"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2"></div>
            </div>
          </div> -->
          <div
            v-if="lot.discount_type == 1"
            class="flex justify-between mt-1 text-sm text-primary"
          >
            <h5>Min {{ numeral(minValue).format("0,0", Math.floor) }}%</h5>
            <h5>Max {{ numeral(maxValue).format("0,0", Math.floor) }}%</h5>
          </div>
        </div>
      </div>
      <div class="relative flex flex-col gap-2 mb-6 text-sm">
        <div class="flex justify-between text-input-icons">
          <div class="flex items-center">
            <inline-svg
              :src="inputIcon"
              class="rotate-180 w-[14px] h-[14px] md:w-4 md:h-4"
            ></inline-svg>
            <h6 class="text-sm text-white md:text-2sm">Output</h6>
          </div>
          <div class="flex flex-row items-center gap-2">
            <inline-svg
              class="stroke-input-icons"
              width="12"
              height="12"
              :src="walletIcon"
            ></inline-svg>
            <div>
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    lot.remaining_listing,
                    currentToken.decimal
                  )
                )
              }}
              {{ lot.token_ticker }}
            </div>
          </div>
        </div>
        <label
          class="input input-bordered h-[48px] flex items-center gap-2"
          :class="
            toBigNumber(buyForm.output).isGreaterThan(
              divideNumberUsingDecimals(lot.remaining_listing, currentToken.decimal)
            )
              ? 'border input-error'
              : 'border border-textbox-border'
          "
        >
          <img
            :src="currentToken.token_image"
            alt="token-img"
            class="w-[20px] h-[20px] rounded-full coin-border"
          />
          <input
            type="number"
            class="w-full grow read-only:opacity-50"
            :step="TOKEN_STEP"
            required
            v-model="buyForm.output"
            :readonly="lot.listing_type == 1"
            @input="handleOutputChange($event.target.value)"
            @keypress="(e) => common.handleNumberInput(e, 6)"
            :disabled="loadingOutput !== null || loadingPercentage !== null"
          />
          <span @click="maxInput" class="text-sm cursor-pointer text-primary"
            >MAX</span
          >
        </label>
        <div class="flex justify-between mt-1">
          <h3 class="text-xs font-medium">
            1 {{ lot.token_ticker }} =
            {{ divideNumberUsingDecimals(lot.best_price ?? lot.listed_price, USDT_DECIMALS) }}
            USDT
          </h3>
          <h3
            v-if="
              toBigNumber(buyForm.output).isGreaterThan(
                divideNumberUsingDecimals(lot.remaining_listing, currentToken.decimal)
              )
            "
            class="text-xs text-error"
          >
            Not enough {{ lot.token_ticker }}
          </h3>
        </div>
      </div>
      <div
        class="flex flex-col justify-center bg-modal-textbox rounded-[8px] text-sm p-4 px-5 h-[90px] gap-[14px] mb-6 text-white/60"
      >
        <div
          class="flex justify-between"
          :class="lot.discount_type == 0 ? 'text-white/60' : 'text-success'"
        >
          <span class="flex items-center">
            <div>{{ discountType[lot.discount_type] }} Discount</div>
            <!-- <span v-if="lot.max_discount * 1 > 0"
                  >(-{{
                    numeral(buyForm.discountPct * 100).format(
                      "0,0.00",
                      Math.floor
                    )
                  }}%)</span
                > -->
            <div
              v-if="lot.discountType == 1"
              class="relative inline-block pl-1 pr-2 group"
            >
              <inline-svg
                :src="infoIcon"
                class="w-3 h-3 stroke-input-icons"
              ></inline-svg>
              <div
                class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
              >
                <div class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll">
                  The linear discount allows you to receive up to
                  {{
                    formatUSDT(buyForm.discountDeduct)
                  }}% off, proportional to the amount of the total supply you
                  purchase. The more you buy, the greater the discount, reaching
                  the full 5% at 100% of the supply.
                </div>

                <!-- Triangle pointer -->
                <div
                  class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"
                ></div>
              </div>
            </div>
          </span>
          <span class="flex items-center gap-1">
            <div>
              {{
                formatUSDT(buyForm.discountDeduct)
              }}
            </div>
            <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
          </span>
        </div>
        <div class="flex justify-between text-white">
          <div>Cost</div>
          <div class="flex items-center gap-1">
            <div>
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    lot.best_price ?? lot.listed_price,
                    USDT_DECIMALS
                  ).multipliedBy(
                    buyForm.output
                  )
                )
              }}
            </div>
            <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <button
          class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 !font-medium disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
          :disabled="
            buyForm.input > props.usdtBalance ||
            toBigNumber(buyForm.output).isGreaterThan(
              divideNumberUsingDecimals(lot.remaining_listing, currentToken.decimal)
            )
          "
        >
          Review Buy
        </button>
      </div>
    </form>
    <!-- <NewModalBuySummary
          :lot="props.lot"
          :buyForm="buyModal?.buyForm"
          :isMarketplaceBuy="true"
          ref="newBuySummaryModal"
      ></NewModalBuySummary> -->
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import { USDT_DECIMALS, TOKEN_STEP, USDT_STEP } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber } from "~/utils/number";

const web3Store = useWeb3Store();
const { refetch } = useMarketplace()

const props = defineProps({
  lot: Object,
  currentToken: Object,
  usdtBalance: Object,
});

const buyForm = reactive({
  input: null,
  output: null,
  discountDeduct: 0,
  discountPct: 0,
  platformFee: 0,
  received: 0,
  fullAmount: 0,
  payAmount: 0,
});

const discountType = {
  0: "No",
  1: "Linear",
  2: "Fixed",
};

const usdtBalance = ref(0);
const loadingInput = ref(null)
const loadingOutput = ref(null)
const loadingPercentage = ref(null)
const emit = defineEmits(["openBuySummary"]);

//slider
const sliderValue = ref(0);
const minValue = ref(0);
const maxValue = ref(30);
const percentage = ref(0);
const slider = ref(null);
const currentLot = ref(props.lot)

// web3Store.$subscribe(async (mutation, state) => {
//   console.log("web3 state", state.isInit);

//   if (state.isInit) {
//     usdtBalance.value = await web3Store.getTokenBalance(
//       useRuntimeConfig().public.usdtAddress
//     );
//   }
// });

async function handleInputChange(newValue) {
  if (loadingOutput.value) clearTimeout(loadingOutput.value)
  loadingOutput.value = setTimeout(async ()=> {
    try {
      const refetched = await refetch()
      const foundLot = refetched.allLots?.find(refetchedLot => {
        // TECH DEPT: we need a better unique identifier to find the lot
        return common.formatLotId(props.lot.token_ticker, props.lot.display_id, props.lot.list_id) === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && props.lot.token_id === refetchedLot.token_id
      })
      currentLot.value = foundLot || currentLot
    
      const bestPrice = divideNumberUsingDecimals(
        currentLot.value.best_price ?? currentLot.value.listed_price,
        USDT_DECIMALS
      );
      const totalListing = divideNumberUsingDecimals(
        currentLot.value.total_listing,
        props.currentToken.decimal
      );
      const discountPct = currentLot.value.max_discount ?? toBigNumber(currentLot.value.discount_pct).dividedBy(100);
      const buyerFee = currentLot.value.buy_fee ?? toBigNumber(currentLot.value.buyer_fee).dividedBy(100);
    
      buyForm.fullAmount = roundToPrecision(toBigNumber(newValue).dividedBy(bestPrice).toNumber());
      buyForm.received = roundToPrecision(toBigNumber(newValue).dividedBy(bestPrice).toNumber());
      buyForm.output = buyForm.received;
      console.log("buyform from handleInputChange", buyForm);
    
      // Update percentage and slider for UI
      percentage.value =
        toBigNumber(buyForm.output)
          .dividedBy(
            divideNumberUsingDecimals(
              currentLot.value.remaining_listing,
              props.currentToken.decimal
            )
          ).multipliedBy(100)
          .toNumber();
      sliderValue.value = (toBigNumber(percentage.value).multipliedBy(maxValue.value).dividedBy(100)).toNumber();
      if (slider.value) {
        document.getElementById(
          "slider"
        ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
      }
    
      calculateFeesAndDiscount(newValue);
    } catch (err) {
      console.error(err)
    } finally {
      loadingOutput.value = null
    }
  }, 200)
}

async function handleOutputChange(newValue) {
  if (loadingInput.value) clearTimeout(loadingInput.value)
  loadingInput.value = setTimeout(async () => {
    try {
      const refetched = await refetch()
      const foundLot = refetched.allLots?.find(refetchedLot => {
        // TECH DEPT: we need a better unique identifier to find the lot
        return common.formatLotId(props.lot.token_ticker, props.lot.display_id, props.lot.list_id) === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && props.lot.token_id === refetchedLot.token_id
      })
      currentLot.value = foundLot || currentLot
      const bestPrice = divideNumberUsingDecimals(
        currentLot.value.best_price ?? currentLot.value.listed_price,
        USDT_DECIMALS
      );
      percentage.value =
        toBigNumber(newValue).dividedBy(
          divideNumberUsingDecimals(
            currentLot.value.remaining_listing,
            props.currentToken.decimal
          )
        ).multipliedBy(100).toNumber();
    
      console.log("percentage handleOutputChange pct value", newValue);
    
      sliderValue.value = (toBigNumber(percentage.value).multipliedBy(maxValue.value).dividedBy(100)).toNumber();
    
      const inputValue = roundToPrecision(toBigNumber(newValue).multipliedBy(bestPrice).toNumber());
      buyForm.input = inputValue;
    
      if (slider.value) {
        document.getElementById(
          "slider"
        ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
      }
    
      handleInputChange(inputValue);
      calculateFeesAndDiscount(inputValue);
    } catch (err) {
      console.error(err)
    } finally {
      loadingInput.value = null
    }
  }, 200)
}

function calculateFeesAndDiscount(inputValue) {
  const totalListing = divideNumberUsingDecimals(
    currentLot.value.total_listing,
    props.currentToken.decimal
  );
  const discountPct = currentLot.value.max_discount ?? currentLot.value.discount_pct / 100;
  const buyerFee = currentLot.value.buy_fee ?? currentLot.value.buyer_fee / 100;

  // Calculate discount based on discount type
  if (currentLot.value.discount_type == 1) {
    // Linear
    const discount = toBigNumber(buyForm.fullAmount).dividedBy(totalListing).multipliedBy(discountPct).toNumber();
    buyForm.discountDeduct = roundToPrecision(toBigNumber(inputValue).multipliedBy(discount).toNumber());
    buyForm.discountPct = roundToPrecision(discount);
  } else if (currentLot.value.discount_type == 2) {
    // Fixed
    buyForm.discountDeduct = roundToPrecision(toBigNumber(inputValue).multipliedBy(discountPct).toNumber());
    buyForm.discountPct = inputValue == 0 ? 0 : discountPct;
  } else {
    // No discount
    buyForm.discountDeduct = 0;
    buyForm.discountPct = 0;
  }

  // Calculate platform fee after discount
  buyForm.platformFee = roundToPrecision(
    toBigNumber(inputValue).minus(buyForm.discountDeduct).multipliedBy(buyerFee).toNumber()
  );

  // Calculate final payment amount
  buyForm.payAmount = roundToPrecision(
    toBigNumber(inputValue).minus(buyForm.discountDeduct).plus(buyForm.platformFee).toNumber()
  );
}

function roundToPrecision(value, precision = 6) {
  return parseFloat(value.toFixed(precision));
}

function updateOutput(lot) {
  console.log("update output lot", lot);
  if (lot.listing_type == 1) {
    buyForm.output = divideNumberUsingDecimals(
      lot.remaining_listing,
      props.currentToken.decimal
    ).toString();
    maxValue.value = 100;
    sliderValue.value = 100;
    percentage.value = 100;
    handleOutputChange(buyForm.output);
    handleInputChange(buyForm.input);
  } else {
    buyForm.input = null;
    buyForm.output = null;
  }
  console.log("buyForm", buyForm);
}

function maxInput() {
  if (
    toBigNumber(props.usdtBalance).dividedBy(divideNumberUsingDecimals(currentLot.value.listed_price, USDT_DECIMALS)).isGreaterThan(
      divideNumberUsingDecimals(currentLot.value.remaining_listing, props.currentToken.decimal)
    )
  ) {
    console.log("max input lot", currentLot);
    buyForm.output = divideNumberUsingDecimals(
      currentLot.value.remaining_listing,
      props.currentToken.decimal
    ).toString();
    handleOutputChange(buyForm.output);
  } else {
    buyForm.input = numeral(props.usdtBalance).format("0.00", Math.floor);
    console.log("max input clicked", currentLot);
    handleInputChange(buyForm.input);
  }
}

async function buyConfirm() {
  const config = web3Store.getConfigByCurrentNetwork()
  const tokenBalance = await web3Store.getTokenBalance(
    config.usdtToken,
    config.network.nativeCurrency.symbol
  );

  console.log("buy usdt balance", tokenBalance);

  if (Number(buyForm.payAmount) > Number(tokenBalance)) {
    //document.getElementById("buyModal").checked=false;

    useSweetAlertStore().showAlert("Error", "Insufficient Token", "error");
    return;
  } else if (
    toBigNumber(buyForm.output).isGreaterThan(
      divideNumberUsingDecimals(
        currentLot.value.remaining_listing,
        props.currentToken.decimal
      )
    )
  ) {
    //document.getElementById("buyModal").checked = false;

    useSweetAlertStore().showAlert("Error", "Exceed maximum amount", "error");
    return;
  }
  //document.getElementById("buyModal").checked = false;
  document.getElementById("newBuySummaryModal").checked = true;
  emit("openBuySummary");
  console.log(document.getElementById("newBuySummaryModal").checked);
  console.log("buy form get", buyForm);
}

const updatePercentage = () => {
  if (loadingPercentage.value) clearTimeout(loadingPercentage.value)
  loadingPercentage.value = setTimeout(async () => {
    try {
      const refetched = await refetch()
      const foundLot = refetched.allLots?.find(refetchedLot => {
        // TECH DEPT: we need a better unique identifier to find the lot
        return common.formatLotId(props.lot.token_ticker, props.lot.display_id, props.lot.list_id) === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && props.lot.token_id === refetchedLot.token_id
      })
      currentLot.value = foundLot || currentLot

      percentage.value = (sliderValue.value / maxValue.value) * 100;
      console.log("percentage", percentage.value);
      // buyForm.input = usdtBalance.value * percentage.value / 100 ;

      buyForm.output =
        (divideNumberUsingDecimals(
          currentLot.value.remaining_listing,
          props.currentToken.decimal
        ).multipliedBy(percentage.value).dividedBy(100).toString());

      console.log("percentagebuyForm.output", buyForm.output);

      handleOutputChange(buyForm.output);

      document.getElementById(
        "slider"
      ).style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
    } catch (err) {
      console.error(err)
    } finally {
      loadingPercentage.value = null
    }
  }, 200)
};

onMounted(async () => {
  // if (web3Store.isInit) {
  //   web3Store
  //     .getTokenBalance(useRuntimeConfig().public.usdtAddress)
  //     .then((bal) => {
  //       usdtBalance.value = bal;
  //     });
  // }
  updateOutput(currentLot);
  // maxValue.value = 100;

  sliderValue.value = currentLot.value.listing_type == 1 ? 100 : 0;
  if (currentLot.value.listing_type == 1) {
    percentage.value = 100;
  } else {
    maxValue.value = currentLot.value.discount_pct;
  }
});

defineExpose({
  updateOutput,
  buyForm,
});
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}
</style>
