<template>
  <div
    class="relative flex justify-center transition-all duration-200 ease-in-out"
  >
  </div>
  <div
    class="bg-white md:min-h-[533px] lg:min-h-[537px] h-full bg-opacity-[6%] md:p-5 lg:p-8 rounded-[12px] font-medium"
  >
    <div
      class="flex flex-col items-start w-full gap-3 pb-1 border-b-2 md:flex-row lg:gap-5 md:pb-6 md:items-center border-textbox-border"
    >
      <img
        width="64px"
        height="64px"
        :src="currentToken?.token_image"
        class="w-12 h-12 rounded-full md:w-16 md:h-16 coin-border"
        alt="token-logo"
      />
      <div class="flex flex-col">
        <h3 class="mb-2 text-white text-2sm md:text-2xl md:mb-0">
          {{ currentToken?.token_name }} <br>
          <span class="text-lg font-satoshiBlack md:text-2xl">
            {{
              formatUSDT(
                divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS)
              )
            }}
            USD</span
          >
        </h3>
        <div class="flex gap-[5px] md:gap-3 items-center md:items-end">
          <h5 class="text-sm md:text-2sm">
            LOT
            {{ lot.marketplace_display_id }}
          </h5>
          <a
            :href="`${useRuntimeConfig().public.nuxt_explorer_dev}${
              currentToken?.token_address
            }`"
            target="_blank"
          >
            <div class="flex gap-1">
              <h5 class="text-primary text-2sm">
                {{ currentToken?.token_ticker }}
              </h5>
              <inline-svg
                class="w-4 fill-primary"
                :src="arrowRightUpIcon"
              ></inline-svg>
            </div>
          </a>
        </div>
      </div>
      <div>
        <h3></h3>
      </div>
    </div>
    <div class="flex flex-col gap-6 mt-10 text-2sm text-input-icons">
      <div class="flex justify-between w-full">
        <h5>Best Price</h5>
        <div class="flex items-center gap-1">
          <h5 class="text-white">
            <!-- {{
              lot?.best_price ?? lot?.listed_price
            }} -->
            {{
              formatUSDT(
                divideNumberUsingDecimals(lot?.listed_price, USDT_DECIMALS)
              )
            }}
          </h5>
          <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
        </div>
      </div>
      <div class="flex justify-between w-full">
        <h5>Lot Balance</h5>
        <h5 class="text-white">
          {{
            formatToken(
              divideNumberUsingDecimals(lot.remaining_listing, currentToken.decimal)
            )
          }}
        </h5>
      </div>
      <div class="flex justify-between w-full">
        <h5>Max Discount to Spot</h5>
        <h5 class="text-success">
          Up to
          {{ numeral(lot?.discount_pct).format("0,0.00", Math.floor) }}%
        </h5>
      </div>
      <div class="flex justify-between w-full">
        <h5>Market Cap</h5>
        <h5 class="text-white uppercase">
          ${{
            numeral(divideNumberUsingDecimals(currentToken.market_cap, currentToken.decimal)).format(
              "0,0a.00"
            )
          }}
        </h5>
      </div>
      <div class="flex justify-between w-full">
        <h5>Max Supply</h5>
        <h5 class="text-white uppercase">
          {{ common.zeroToNil(numeral(divideNumberUsingDecimals(currentToken.max_supply, currentToken.decimal).toString()).format("0,0a.00")) }}
        </h5>
      </div>
      <div class="flex justify-between w-full uppercase">
        <h5>FDV</h5>
        <h5 class="text-white">
          ${{ numeral(divideNumberUsingDecimals(currentToken.fdv, currentToken.decimal).toString()).format("0,0a.00") }}
        </h5>
      </div>
      <div class="flex justify-between w-full">
        <h5>Market Price</h5>
        <h5 class="text-white">
          {{
            common.zeroToNil(
              numeral(divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS).toString()).format(
                "0,0a.00"
              ),
              true
            )
          }}
          <span
            :class="`text-sm ${
              currentToken.twf_hour_changes * 1 > 0
                ? 'text-success'
                : 'text-error'
            }`"
            >{{ common.twfSymbol(currentToken.twf_hour_changes * 1 > 0)
            }}{{ currentToken.twf_hour_changes }}%
          </span>
        </h5>
      </div>
      <div class="flex justify-between w-full">
        <h5>First Unlock Begins</h5>
        <h5 class="text-white">

          {{
            lot.plan_function === 'CYCLE' ?
            $dayjs(lot?.start_date * 1)
              .add(lot?.cliff_duration, timeUnit)
              .add(lot?.duration, timeUnit)
              .format("DD/MM/YYYY")
            :
            $dayjs(lot?.start_date * 1)
              .add(lot?.cliff_duration, timeUnit)
              .format("DD/MM/YYYY")
          }}
          <span class="text-xs text-white/50">{{
            getCountDown(
              $dayjs(lot?.start_date * 1)
                .add(lot?.cliff_duration, timeUnit)
                .format("x") * 1
            )
          }}</span>
        </h5>
      </div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import arrowRightUpIcon from "~/assets/images/icons/arrow-right-up.svg";
import { USDT_DECIMALS, TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, formatToken } from "~/utils/number";

const { $dayjs } = useNuxtApp();

const props = defineProps({
  lot: Object,
  currentToken: Object,
  maxSupply: Object,
  getCountDown: Function,
});
const timeUnit = TIME_UNIT;

</script>

<style></style>
