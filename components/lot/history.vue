<template>
  <div class="mt-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center justify-between">
        <div class="flex gap-8 md:gap-10 text-2sm">
          <h5
            :class="`${
              historyTab === 'similarLots'
                ? 'text-white tab-active relative'
                : 'text-white/50'
            } relative cursor-pointer`"
            @click="historyTab = 'similarLots'"
          >
            <!-- <h5
            :class="`${
              (historyTab === 'similarLots' 
                ? (!lots || lots.length === 0 ? 'text-[#535561] tab-active relative' : 'text-white tab-active relative') 
                : 'text-white/50') 
            } relative cursor-pointer`"
            @click="historyTab = 'similarLots'"
          > -->
            Similar Lots
          </h5>
          <h5
            :class="`${
              historyTab === 'transactionHistory'
                ? 'text-white tab-active relative'
                : 'text-white/50'
            } relative cursor-pointer`"
            @click="historyTab = 'transactionHistory'"
          >
            Transaction History
          </h5>
          <!-- <h5
            :class="`${
              historyTab === 'transactionHistory' 
                ? (!histories || histories.length === 0 ? 'text-[#535561] tab-active relative' : 'text-white tab-active relative') 
                : 'text-white/50'
            } relative cursor-pointer`"
            @click="historyTab = 'transactionHistory'"
          >Transaction History
          </h5> -->
        </div>
      </div>
      <div class="flex items-center gap-8">
        <div class="hidden md:block">
          <div
            v-if="historyTab == 'transactionHistory'"
            class="flex items-center gap-2 text-sm"
          >
            <input
              type="checkbox"
              v-model="currentLot"
              @change="filterCurrentLot"
              class="checkbox checkbox-primary h-4 w-4 rounded-[2px]"
            />
            <h5>Current Lot Only</h5>
          </div>
        </div>

        <div
          class="flex justify-center md:justify-start items-center gap-2 rounded-full border border-white/20 md:px-4 md:py-1 w-8 h-8 md:h-10 md:w-[220px]"
        >
          <inline-svg
            @click="openSearchModal"
            :src="searchIcon"
            class="w-4 h-4 md:min-w-5 md:h-5 stroke-input-icons"
          ></inline-svg>
          <input
            class="input h-8 min-h-8 bg-transparent border-none focus:ring-0 focus:outline-none p-0 text-2sm hidden md:block w-[80%]"
            type="text"
            placeholder="Search by Lot"
            @keyup="searchLot"
            v-model="searchDisplayId"
          />
        </div>
      </div>
    </div>

    <hr class="border-[1px] border-textbox-border mt-6 mb-4 hidden lg:block" />

    <div class="mt-5 md:hidden">
      <div
        v-if="historyTab == 'transactionHistory'"
        class="flex items-center gap-2 text-sm"
      >
        <input
          type="checkbox"
          v-model="currentLot"
          @change="filterCurrentLot"
          class="checkbox checkbox-primary h-4 w-4 rounded-[2px]"
        />
        <h5>Current Lot Only</h5>
      </div>
    </div>
    <div>
      <div v-if="historyTab == 'transactionHistory'">
        <table
          class="hidden font-medium border-separate table-fixed lg:table text-2sm border-spacing-y-3"
        >
          <thead
            class="w-full text-sm"
            :class="`${
              !histories || histories.length == 0
                ? 'text-[#535561]'
                : 'text-input-icons'
            }`"
          >
            <tr>
              <th class="w-[20%] pl-0">Lot</th>
              <th class="w-[17%]">Date</th>
              <th class="w-[15%]">TXID</th>
              <th class="w-[15%]">Buyer</th>
              <th class="w-[15%]">Seller</th>
              <th class="w-[10%]">Amount</th>
              <th class="w-[8%] text-right">Price</th>
            </tr>
          </thead>
          <tbody class="font-medium text-white">
            <tr v-if="!histories || histories.length == 0">
              <td colspan="7">
                <NoData
                  :isHistory="true"
                  :content="'No Transaction History'"
                ></NoData>
              </td>
            </tr>
            <tr v-else v-for="(history, index) in histories">
              <td class="pl-0">
                <div class="flex gap-3">
                  <div class="relative w-[44px] h-[44px] flex-shrink-0">
                    <img
                      class="w-full h-full rounded-full coin-border"
                      :src="history.token_image"
                      alt="token-img"
                    />

                    <img
                      class="absolute bottom-0 w-[16px] h-[16px]"
                      :src="history.network_image"
                      alt="network-img"
                    />
                  </div>
                  <div class="flex flex-col">
                    <h5 class="text-2sm">{{ history.token_ticker }}</h5>
                    <h5 class="text-sm text-input-icons">
                      {{ history.token_name }}
                      <span class="text-xs font-satoshiLight"
                        >#{{
                          history.marketplace_display_id ??
                          history.market_display_id
                        }}</span
                      >
                    </h5>
                  </div>
                </div>
              </td>
              <td>
                <div>
                  {{ common.formatDateTimeV3Date(history.txn_date) }}
                  <span class="text-sm text-input-icons">
                    {{ common.formatDateTimeV3Time(history.txn_date) }}
                  </span>
                </div>
              </td>
              <td>
                <div
                  class="flex items-center gap-2 cursor-pointer text-primary w-fit"
                  @click.stop="redirectToExplorer(history.hash, 'tx')"
                >
                  {{ common.formatAddress(history.hash) }}
                  <inline-svg
                    class="w-4 h-4 stroke-primary"
                    :src="arrowUpRightIcon"
                  ></inline-svg>
                </div>
              </td>
              <td>
                <div
                  class="flex items-center gap-2 cursor-pointer text-primary w-fit"
                  @click.stop="redirectToExplorer(history.buyer, 'address')"
                >
                  {{ common.formatAddress(history.buyer) }}
                  <inline-svg
                    class="w-4 h-4 stroke-primary"
                    :src="arrowUpRightIcon"
                  ></inline-svg>
                </div>
              </td>
              <td>
                <div
                  class="flex items-center gap-2 cursor-pointer text-primary w-fit"
                  @click.stop="redirectToExplorer(history.seller, 'address')"
                >
                  {{ common.formatAddress(history.seller) }}
                  <inline-svg
                    class="w-4 h-4 stroke-primary"
                    :src="arrowUpRightIcon"
                  ></inline-svg>
                </div>
              </td>
              <td>
                <div>
                  {{
                    numeral(
                      divideNumberUsingDecimals(
                        history.amount,
                        history.token_decimal
                      ).toString()
                    ).format("0,000.0000")
                  }}
                </div>
              </td>
              <td class="text-right">
                <div>
                  ${{
                    formatUSDT(
                      divideNumberUsingDecimals(history.price, USDT_DECIMALS).multipliedBy(
                        divideNumberUsingDecimals(
                          history.amount,
                          history.token_decimal
                        )
                      )
                    )
                  }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-if="!histories || histories.length == 0" class="lg:hidden">
          <NoData :content="'No Transaction History'"></NoData>
        </div>
      </div>
      <div v-else-if="historyTab == 'similarLots'">
        <table
          class="hidden font-medium border-separate table-fixed lg:table text-2sm border-spacing-y-3"
        >
          <thead class="font-medium text-2sm">
            <tr class="border-b-0">
              <th class="w-[22%] pl-0">Lot</th>
              <th class="w-[15%]">
                <div class="flex">
                  <h5>Price</h5>
                  <inline-svg
                    class="w-[16px] h-[16px]"
                    :src="sortIcon"
                  ></inline-svg>
                </div>
              </th>
              <th class="w-[15%]">
                <div class="flex">
                  <h5>Best Price</h5>
                  <inline-svg
                    class="w-[16px] h-[16px]"
                    :src="sortIcon"
                  ></inline-svg>
                </div>
              </th>
              <th class="w-[15%]">
                <div class="flex">
                  <h5>Discount</h5>
                  <inline-svg
                    class="w-[16px] h-[16px]"
                    :src="sortIcon"
                  ></inline-svg>
                </div>
              </th>
              <th class="w-[16%]">
                <div class="flex">
                  <h5>Unlock Start</h5>
                  <inline-svg
                    class="w-[16px] h-[16px]"
                    :src="sortIcon"
                  ></inline-svg>
                </div>
              </th>
              <th class="w-[7%] text-left p-0">%</th>
              <th class="w-[14%] text-right pr-2"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!lots || lots.length == 0">
              <td colspan="7">
                <NoData :content="'No Similar Lots'"></NoData>
              </td>
            </tr>
            <tr
              v-else
              v-for="(lot, index) in lots"
              class="transition-all duration-300 border-b-0 hover:bg-white/10"
              @click="toLotInfo(lot)"
            >
              <td class="pl-0 rounded-s-lg">
                <div class="flex items-center gap-4">
                  <div class="relative w-[44px] h-[44px] flex-shrink-0">
                    <img
                      class="flex-shrink-0 w-full h-full rounded-full coin-border"
                      :src="lot.token_image"
                      alt="token-img"
                    />

                    <img
                      class="absolute bottom-0 w-[16px] h-[16px]"
                      :src="lot.network_image"
                      alt="network-img"
                    />
                  </div>

                  <div>
                    <h3>{{ lot.token_ticker }}</h3>
                    <div class="flex items-center gap-1">
                      <h5
                        class="text-sm text-white/50 truncate max-w-[50px] 2lg:truncate-none 2lg:max-w-full"
                      >
                        {{ lot.token_name }}
                      </h5>
                      <h5 class="text-xs text-white/50 font-satoshiLight">
                        #{{ lot.marketplace_display_id }}
                      </h5>
                    </div>
                  </div>
                </div>
              </td>

              <td>
                <h5>
                  {{
                    formatUSDT(
                      divideNumberUsingDecimals(
                        lot.token_price,
                        USDT_DECIMALS
                      )
                    )
                  }}
                  USDT
                </h5>
                <h5
                  :class="`text-sm ${
                    twfChangesInfo[lot.twf_hour_changes * 1 > 0].color
                  }`"
                >
                  {{ twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                  }}{{ lot.twf_hour_changes.replace("-", "") }}%
                </h5>
              </td>

              <td>
                <h5>
                  {{
                    numeral(
                      divideNumberUsingDecimals(
                        lot.listed_price,
                        USDT_DECIMALS
                      ).multipliedBy(
                        toBigNumber(1).minus(toBigNumber(lot.discount_pct).dividedBy(100))
                      )
                    ).format("0,0.00a", Math.floor)
                  }}
                  USDT
                </h5>
              </td>

              <td>
                <h5>
                  Up to
                  {{ formatToken(lot.discount_pct) }}%
                </h5>
              </td>

              <td class="whitespace-nowrap">
                <h5>
                  {{
                    lot.plan_function === 'CYCLE' ?
                    $dayjs(lot.start_date * 1)
                      .add(lot.cliff_duration, timeUnit)
                      .add(lot.duration, timeUnit)
                      .format("DD/MM/YYYY")
                    :
                    $dayjs(lot.start_date * 1)
                      .add(lot.cliff_duration, timeUnit)
                      .format("DD/MM/YYYY")
                  }}
                  <span class="text-xs text-white/50">{{
                    getCountDown(
                      lot.plan_function === 'CYCLE' ?
                      $dayjs(lot.start_date * 1)
                        .add(lot.cliff_duration, timeUnit)
                        .add(lot.duration, timeUnit)
                        .format("x") * 1
                      :
                      $dayjs(lot.start_date * 1)
                        .add(lot.cliff_duration, timeUnit)
                        .format("x") * 1
                    )
                  }}</span>
                </h5>
              </td>

              <td class="p-0 text-left whitespace-nowrap">
                {{
                  lot.listing_type == 0
                    ? `${numeral(
                        ((lot.total_listing - lot.remaining_listing) /
                          lot.total_listing) *
                          100
                      ).format("0.00")}%`
                    : "Single Fill"
                }}
              </td>

              <td class="font-medium text-right rounded-e-lg">
                <button
                  class="btn btn-primary h-[26px] min-h-[26px] min-w-[68px] !rounded-2sm text-2sm"
                >
                  Buy
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-if="!lots || lots.length == 0" class="md:hidden">
          <NoData :content="'No Similar Lots'"></NoData>
        </div>
        <div v-else v-for="(lot, index) in lots" class="mb-4 md:mt-5 lg:hidden">
          <CardAllLot
            @click="toLotInfo(lot)"
            :lot="lot"
            :index="index"
            :planId="lot.plan_id"
            :tokenImg="lot.token_image"
            :networkImg="lot.network_image"
            :networkSymbol="lot.network_symbol"
            :totalListing="lot.total_listing"
            :remainingListing="lot.remaining_listing"
            :tokenName="lot.token_name"
            :tokenTicker="lot.token_ticker"
            :listId="lot.list_id"
            :tokenPrice="
              divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS).toString()
            "
            :bestPrice="
              divideNumberUsingDecimals(lot.listed_price, USDT_DECIMALS).toString()
            "
            :maxDiscount="lot.discount_pct"
            :unlockStart="lot.start_date ?? 0"
            :displayId="lot.display_id"
            :twfHourChanges="`${
              twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
            }${lot.twf_hour_changes.replace('-', '')}%`"
            :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
            :lotId="
              common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)
            "
            :buttonText="'Buy'"
          ></CardAllLot>
          <ModalLotDetailsSearch
            ref="searchLotModalRef"
            v-model="isSearchModalVisible"
            @closeModal="closeSearchModal"
            :twfChangesInfo="props.twfChangesInfo"
            :lots="lots"
            :tokenAddress="props.token_address"
            :sort_by="historyTab"
            :displayId="display_id?.value"
          ></ModalLotDetailsSearch>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import arrowUpRightIcon from "~/assets/images_new/icons/arrow-up-right.svg";
import searchIcon from "~/assets/images_new/icons/search.svg";
import { useRoute } from "vue-router";
import sortIcon from "~/assets/images_new/icons/sort.svg";
import { USDT_DECIMALS, TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber, formatToken } from "~/utils/number";

const props = defineProps({
  marketplace_display_id: Object,
  token_address: Object,
  getCountDown: Function,
  twfChangesInfo: Object,
});

const { $dayjs } = useNuxtApp();

const web3Store = useWeb3Store();

const histories = ref(null);
const lots = ref(null);
const currentLot = ref(true);
const display_id = ref(null);
const route = useRoute();
const historyTab = ref("transactionHistory");
const searchDisplayId = ref(null);
const searchLotModalRef = ref(null);
const router = useRouter();
const timeUnit = TIME_UNIT;

let searchTimeout;

async function getHistory() {
  try {
    const apiRes = await api.apiCall(
      "GET",
      "/market-transaction/all-transactions",
      {
        order: "DESC",
        page: 1,
        take: 10,
        network: route.query.network || web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol,
        token_address: props.token_address,
        marketplace_display_id: display_id.value,
      }
    );

    histories.value = apiRes.data.message.data;
    console.log("transaction history", apiRes.data.message);
  } catch (error) {
    console.error(error);
  }
}

async function getLots() {
  try {
    const res = await api.apiCall("GET", "/marketplace/all-lot", {
      // network: useRuntimeConfig().public.web3Network,
      take: 50,
      token_address: props.token_address,
      exclude_marketplace_id: display_id.value,
      marketplace_display_id: searchDisplayId.value,
    });
    console.log("top tokens res", res.data);

    lots.value = res.data.message.lots.data;
  } catch (error) {
    console.error("top tokens error", error);
  }
}

const filterCurrentLot = async () => {
  if (currentLot.value) {
    display_id.value = props.marketplace_display_id;
  } else {
    display_id.value = null;
  }
  console.log("current lot", currentLot.value, "display", display_id.value);
  await getHistory();
};

function searchLot() {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  searchTimeout = setTimeout(async () => {
    console.log("search token function called with:", searchDisplayId.value);
    const res = await getLots();
    lots.value = res.data.message.lots.data;
  }, 500);
}

const isSearchModalVisible = ref(false);
function openSearchModal() {
  isSearchModalVisible.value = false;
  searchDisplayId.value = null;
  isSearchModalVisible.value = true;
}

async function closeSearchModal() {
  isSearchModalVisible.value = false;
  searchLotModalRef.value.searchDisplayId = null;
}

watch(historyTab, (newValue, oldValue) => { 
  if (newValue == "transactionHistory") {
    getHistory();
    console.log("transaction history clicked", historyTab);
  } else if (newValue == "similarLots") {
    display_id.value = props.marketplace_display_id;
    getLots();
    console.log("similar lots clicked", historyTab);
  }
});

onMounted(async () => {
  display_id.value = props.marketplace_display_id;
  await getHistory();
});

async function toLotInfo(lot) {
  console.log("to lot", lot);
  router.push(`/${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`);
}

const redirectToExplorer = (hash, type) => {
  const baseUrl = useRuntimeConfig().public.explorerUrl;
  const url =
    type === "tx" ? `${baseUrl}/tx/${hash}` : `${baseUrl}/address/${hash}`;
  window.open(url, "_blank");
};
</script>

<style scoped></style>
