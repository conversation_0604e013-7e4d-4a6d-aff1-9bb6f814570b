<template>
  <div v-if="isCopied" class="relative flex justify-center">
    <transition name="fade">
      <div
        class="absolute -top-5 flex justify-center items-center gap-2 bg-[#50F18712] text-[#50F187] text-2sm w-[155px] h-[44px] rounded-[8px]"
      >
        <inline-svg
          width="16"
          height="16"
          :src="checkCircleIcon"
        ></inline-svg>
        <h6>Link Copied</h6>
      </div>
    </transition>
  </div>
  <div class="collapse bg-white bg-opacity-[6%] font-medium rounded-[12px] relative">
    <input @click.stop="toggleDetails" type="checkbox"
      class="absolute w-16 min-h-16 max-h-16"
      :class="detailsOpen 
              ? 'absolute w-1/5 top-[170px] right-0' 
              : 'absolute w-1/5 bottom-0 right-0'"
    />
    <div class="p-5 collapse-title">
      <div class="flex flex-col items-start w-full pb-6 border-b border-textbox-border" >
        <div class="flex items-center justify-between w-full mb-5">
          <img
            width="64px"
            height="64px"
            :src="currentToken?.token_image"
            class="w-12 h-12 rounded-full "
            alt="token-logo"
          />
          <inline-svg 
            :data-clipboard-text="link"
            :src="shareIcon" class="w-5 h-5 copy-link"></inline-svg>
        </div>
          <div class="flex flex-col">
            <h3 class="mb-2 text-white text-2sm md:text-2xl md:mb-0">{{ currentToken?.token_name }} 
              <span class="text-lg font-satoshiBlack md:text-2xl">
                {{
                  formatUSDT(divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS))
                }} USD</span>
            </h3>
            <div class="flex gap-[5px] md:gap-3 items-center md:items-end">
              <h5 class="text-sm md:text-2sm">
                  LOT
                  {{
                    lot.marketplace_display_id
                  }}
              </h5>
              <a
                :href="`${useRuntimeConfig().public.nuxt_explorer_dev}${currentToken?.token_address}`"
                target="_blank"
              >
                <div class="flex gap-1">
                  <h5 class="text-primary text-2sm">
                    {{ currentToken?.token_ticker }} 
                  </h5>
                  <inline-svg
                    class="w-4 fill-primary"
                    :src="arrowRightUpIcon"
                  ></inline-svg>
                </div>
              </a>
            </div>
          </div>
      </div>
      <div class="flex justify-between mt-6 ">
        <h5 class="text-xs tracking-[3px] text-white">MORE DETAILS</h5>
        <inline-svg
          width="16"
          :src="chevronDown"
          :style="{
            transform: detailsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
          }"
        />
      </div>
    </div>
    <div class="px-5 collapse-content">
      <!-- more details -->
      <div class="flex flex-col gap-5 pb-1 text-2sm text-input-icons">
        <div class="flex justify-between w-full">
          <h5>Best Price</h5>
          <div class="flex items-center gap-1">
            <h5 class="text-white">
              <!-- {{
                lot?.best_price ?? lot?.listed_price
              }} -->
              {{
                formatUSDT(
                  divideNumberUsingDecimals(lot?.listed_price, USDT_DECIMALS)
                )
              }}
            </h5>
            <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
          </div>
        </div>
        <div class="flex justify-between w-full">
          <h5>Lot Balance</h5>
          <h5 class="text-white">
            {{
              formatToken(
                divideNumberUsingDecimals(lot.remaining_listing, currentToken.decimal)
              )
            }}
          </h5>
        </div>
        <div class="flex justify-between w-full">
          <h5>Max Discount to Spot</h5>
          <h5 class="text-success">
            Up to
            {{ formatToken(lot?.discount_pct) }}%
          </h5>
        </div>
        <div class="flex justify-between w-full">
          <h5>Market Cap</h5>
          <h5 class="text-white uppercase">
            ${{
              numeral(
                divideNumberUsingDecimals(currentToken.market_cap, USDT_DECIMALS).toString()
              ).format("0,0a.00")
            }}
          </h5>
        </div>
        <div class="flex justify-between w-full">
          <h5>Max Supply</h5>
          <h5 class="text-white uppercase">
            {{
              common.zeroToNil(numeral(maxSupply).format("0,0a.00"))
            }}
          </h5>
        </div>
        <div class="flex justify-between w-full uppercase">
          <h5>FDV</h5>
          <h5 class="text-white">
            ${{
              numeral(divideNumberUsingDecimals(lot.fdv, USDT_DECIMALS).toString()).format("0,0a.00")
            }}
          </h5>
        </div>
        <div class="flex justify-between w-full">
          <h5>Market Price</h5>
          <h5 class="text-white">
            {{ 
              common.zeroToNil(
                numeral(divideNumberUsingDecimals(currentToken.price, USDT_DECIMALS).toString()).format(
                  "0,0a.00"
                ),
                true
              )
            }}
            <span
              :class="`text-sm ${
                currentToken.twf_hour_changes * 1 > 0
                  ? 'text-success'
                  : 'text-error'
              }`"
              >{{ common.twfSymbol(currentToken.twf_hour_changes * 1 > 0)
              }}{{ currentToken.twf_hour_changes }}%
            </span>
          </h5>
        </div>
        <div class="flex justify-between w-full">
          <h5><span class="hidden md:block">First</span> Unlock Begins</h5>
          <h5 class="text-white">
            {{
              $dayjs(lot?.start_date * 1)
                .add(lot?.cliff_duration, timeUnit)
                .format("DD/MM/YYYY")
            }}
            <span class="text-xs text-white/50">{{
              getCountDown(
                $dayjs(lot?.start_date * 1)
                  .add(lot?.cliff_duration, timeUnit)
                  .format("x") * 1
              )
            }}</span>
          </h5>
        </div>
        
      </div>
    </div>
    
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";

import arrowRightUpIcon from "~/assets/images/icons/arrow-right-up.svg";
import shareIcon from "~/assets/images_new/icons/share.svg";
import chevronDown from "~/assets/images_new/icons/chevron-down.svg";
import ClipboardJS from "clipboard";
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, formatToken } from "~/utils/number";

const { $dayjs } = useNuxtApp();

const props = defineProps({
  lot: Object,
  currentToken: Object,
  maxSupply: Object,
  getCountDown: Function,
});

const detailsOpen = ref(false);
const link = ref("");

function toggleDetails(){
  detailsOpen.value = !detailsOpen.value;
}

const isCopied = ref(false);
onMounted(async () => {
  link.value = window.location.href;

  //clipboard
  var clipboard = new ClipboardJS('.copy-link');
  clipboard.on('success', function (e) {
    isCopied.value = true;
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
    e.clearSelection();
  });

  clipboard.on('error', function (e) {
    console.error('Action:', e.action);
    console.error('Trigger:', e.trigger);
  });
});


</script>

<style>

</style>
