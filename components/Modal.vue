<template>
	<dialog :open="opened" :class="['md:translate-y-0']" role="dialog"
		class="lg:mt-10 modal bg-black/20 backdrop-blur-[2px] backdrop:bg-transparent font-satoshiMedium">
		<div
			:class="['modal-box w-full pt-8 px-5 pb-6 bg-modal md:rounded-[12px] border border-textbox-border shadow-[0_0px_14px_0px_rgba(255,255,255,0.08)] fixed bottom-0 md:static transform transition-all duration-500 ease-out', modalBodyClass]">
			<div class="flex items-center justify-between mb-8">
				<h3 class="text-xl">
					{{ title }}
				</h3>

				<button class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none"
					@click="handleClose">
					✕
				</button>
			</div>
			<slot />
		</div>
	</dialog>
</template>

<script setup>
const props = defineProps({
	opened: String,
	title: String,
	modalBodyClass: String,
})

const emit = defineEmits(["onClose"])

const handleClose = () => {
	emit("onClose")
}
</script>

<style scoped></style>
