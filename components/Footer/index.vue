<template>
  <div
    class="flex flex-col-reverse items-center justify-center gap-8 py-10 mt-4 border-t lg:py-4 border-white/10 lg:gap-0 lg:flex-row lg:justify-between "
  >
    <div class="flex items-center gap-4">
      <h5 class="text-sm">Audited by:</h5>
      <a href="">
        <img class="w-[82px] h-[16px]" :src="code4rena" alt="auditor" />
      </a>

      <a href="">
        <img class="w-[50.6px] h-[14px]" :src="zellic" alt="auditor" />
      </a>
    </div>

    <div class="hidden mr-16 lg:block">
      <a href="">
        <img
          class="w-[120px] h-[24px]"
          :src="secondswapFullLogo"
          alt="2sFullLogo"
        />
      </a>
    </div>

    <div class="flex items-center gap-4">
      <a href="https://www.linkedin.com/company/secondswap/" class="bg-white/10 flex items-center justify-center w-[30px] h-[30px] rounded-full">
        <inline-svg
          class="w-[16px] h-[16px] fill-white"
          :src="linkinIcon"
        ></inline-svg>
      </a>

      <a href="https://x.com/secondswap_io" class="bg-white/10 rounded-full flex items-center justify-center w-[30px] h-[30px]">
        <inline-svg
          class="w-[16px] h-[16px] fill-white"
          :src="xIcon"
        ></inline-svg>
      </a>

      <a href="https://t.me/secondswap_community" class="bg-white/10 rounded-full flex items-center justify-center w-[30px] h-[30px]">
        <inline-svg
          class="w-[18px] h-[18px] fill-white"
          :src="telegramIcon"
        ></inline-svg>
      </a>
    </div>

    
    <div class="lg:hidden">
      <a href="">
        <img
          class="w-[120px] h-[24px]"
          :src="secondswapFullLogo"
          alt="2sFullLogo"
        />
      </a>
    </div>

  </div>
</template>

<script setup>
import code4rena from "~/assets/images_new/auditor/code4rena_1_5x.webp";
import zellic from "~/assets/images_new/auditor/zellic_1_5x.webp";
import secondswapFullLogo from "~/assets/images_new/common/2s-full-logo_1_5x.webp";

import linkinIcon from "~/assets/images_new/icons/social_media/linkedin.svg";
import xIcon from "~/assets/images_new/icons/social_media/x.svg";
import telegramIcon from "~/assets/images_new/icons/social_media/telegram.svg";

import InlineSvg from "vue-inline-svg";

</script>

<style scoped></style>
