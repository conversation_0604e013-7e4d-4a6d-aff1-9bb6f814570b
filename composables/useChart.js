import { TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals } from "~/utils/number";

export const useChart = (selectedPlan, currentToken) => {

  console.log('chart data', selectedPlan.value.amount, currentToken);

  const web3Store = useWeb3Store();
  const timeUnit = TIME_UNIT;
  const chart = ref(null);
  const isChartReady = ref(false);
  const { $dayjs, $chart } = useNuxtApp();

  // Function to generate dates based on a variable interval
  function generateDatesWithInterval(startDate, endDate, intervalDays) {
    const startDay = $dayjs(startDate * 1); // Parse start date from Unix timestamp
    const endDay = $dayjs(endDate * 1); // Parse end date from Unix timestamp
    const dateArray = [];

    // Loop to generate dates based on the specified interval
    for (
      let currentDate = startDay;
      currentDate.isBefore(endDay) || currentDate.isSame(endDay);
      currentDate = currentDate.add(intervalDays, timeUnit)
    ) {
      dateArray.push(currentDate); // Format and push to array
    }

    return dateArray;
  }

  function getXaxis(dateGenerated) {
    const xAxis = dateGenerated.map((date) => {
      return date.format("DD-MM-YYYY");
    });

    return xAxis;
  }

  function getYaxis(dateGenerated) {
    const yAxis = [];
    const diff = $dayjs(selectedPlan.value.end_date * 1).diff(
      $dayjs(selectedPlan.value.start_date * 1),
      timeUnit
    );
    const dropPeriod = diff - selectedPlan.value.cliff_duration;
    const cliffEnd = $dayjs(selectedPlan.value.start_date * 1)
      .add(selectedPlan.value.cliff_duration, timeUnit)
      .format("x");

    let amount = 0;

    let cliffEndIndex = 0;

    if (selectedPlan.value.function == 'TIME') {
      // TIME
      const dropPerOne =
        divideNumberUsingDecimals(selectedPlan.value.total_allocated, selectedPlan.value.token_decimal).dividedBy(dropPeriod).toNumber();

      dateGenerated.forEach((item, index) => {
        const convertTime = item.format("x");

        if (convertTime <= cliffEnd) {
          yAxis.push(0);
        } else {
          if (cliffEndIndex == 0) {
            cliffEndIndex = index - 1;
          }
          amount += dropPerOne;
          yAxis.push(amount);
        }
      });

    } else {
      // CYCLE
      const dropPerOne =
        divideNumberUsingDecimals(selectedPlan.value.total_allocated, selectedPlan.value.token_decimal).dividedBy(selectedPlan.value.cycle).toNumber();

      console.log('drop per one', dropPerOne, selectedPlan.value.cycle);
      console.log('cycle cliff end', cliffEnd);

      dateGenerated.forEach((item, index) => {
        const convertTime = item.format("x");

        console.log('cycle convert time', {
          convertTime: convertTime,
          cliffEnd: cliffEnd,
          compare: convertTime * 1 <= cliffEnd * 1
        });

        if (convertTime * 1 <= cliffEnd * 1) {
          yAxis.push(0);
        } else {
          if (cliffEndIndex == 0) {
            cliffEndIndex = index - 1;
          }
          const convertMinute = item.day();

          if (convertMinute % selectedPlan.value.duration == 0) {
            amount += dropPerOne;
          }

          console.log('cycle amount', amount);

          yAxis.push(amount);
        }
      });
    }

    console.log('cycle yAxis', yAxis);

    return { yAxis: yAxis, cliffEndIndex: cliffEndIndex };
  }

  function generateChart() {

    isChartReady.value = false;

    if (chart.value) {
      chart.value.destroy();
    }


    let endTime = $dayjs(selectedPlan.value.start_date * 1)
      .add(
        selectedPlan.value.duration * selectedPlan.value.cycle +
        selectedPlan.value.cliff_duration * 1,
        timeUnit
      )
      .format("x");

    const dateGenerated = generateDatesWithInterval(
      selectedPlan.value.start_date,
      selectedPlan.value.function == 'TIME' ? selectedPlan.value.end_date : endTime,
      1
    );

    const xAxis = getXaxis(dateGenerated);
    const { yAxis, cliffEndIndex } = getYaxis(dateGenerated);

    const cliffLabel = {
      type: "label",
      content: cliffEndIndex > 1 ? "Cliff Period" : "",
      color: "rgb(232, 93, 93)",
      // font: {
      //   size: 16,
      // },
      padding: {
        bottom: 15,
      },
      position: {
        x: "center",
        y: "end",
      },
      xValue: xAxis[Math.floor(cliffEndIndex / 2)],
      yValue: yAxis[0],
    };

    const annotation = {
      cliffLabel,
    };

    const ctx = document.getElementById("planChart");
    chart.value = new $chart(ctx, {
      type: "line",
      data: {
        labels: xAxis,
        borderColor: "#FFFF",
        datasets: [
          {
            label: currentToken.value.token_ticker,
            data: yAxis,
            // borderColor : ["rgba(232, 93, 93, 0.5)"],
            pointBackgroundColor: function (context) {
              const index = context.dataIndex;
              // Ensure point fill color follows the line color
              const color =
                index < cliffEndIndex ? "rgba(232, 93, 93, 1)" : "#48C3F8"; // Red before, Blue after
              return color;
            },
            pointBorderColor: function (context) {
              const index = context.dataIndex;
              // Ensure point border color follows the line color
              const color =
                index < cliffEndIndex ? "rgba(232, 93, 93, 1)" : "#48C3F8"; // Red before, Blue after
              return color;
            },

            segment: {
              borderColor: function (context) {
                // console.log(context);
                const index = context.p0DataIndex;

                // Change color after index 5
                return index < cliffEndIndex
                  ? "rgba(232, 93, 93, 0.5)"
                  : "#48C3F8"; // Red before, Blue after
              },

              // backgroundColor: function (context) {
              //   // console.log(context);
              //   const index = context.p0DataIndex;
              //   // Change color after index 5
              //   return index < cliffEndIndex ? "rgba(232, 93, 93, 0.5)" : "#48C3F8"; // Red before, Blue after
              // },
            },
            borderWidth: 2,
            fill: true,
          },
        ],
      },
      options: {
        scales: {
          y: {
            title: {
              display: true,
              text: "Token Release", // Add your title here
              align: "center",
              color: "#FFFF",
            },
            grid: {
              color: "rgba(255, 255, 255, 0.2)", // Red grid lines for X-axis
            },
          },
          x: {
            title: {
              display: true,
              text: "Vesting Duration", // Add your title here
              align: "center",
              color: "#FFFF",
            },
            ticks: {
              maxTicksLimit: 15, // Limits the number of visible x-axis labels
            },
          },
        },

        responsive: true,
        maintainAspectRatio: false,
        animation: {
          onComplete: function (e) {
            isChartReady.value = true;
            // drawVerticalLine(chart.value, cliffEndIndex);
          },
        },
        plugins: {
          annotation: {
            annotations:
              // line1: {
              //   type: "line",
              //   yMin: 60,
              //   yMax: 60,
              //   borderColor: "rgb(255, 99, 132)",
              //   borderWidth: 2,
              // },
              annotation,
          },
          legend: {
            display: false,
            position: "top",
          },
          tooltip: {
            mode: "index",
            intersect: false,
          },
          fixedLine: {
            yPosition: 30, // Fixed y-axis value
            color: "rgba(0, 255, 0, 1)", // Line color (green)
          },
        },
      },
    });

    console.log('chart value', chart.value);

  }

  return {
    generateChart, chart
  }
}
