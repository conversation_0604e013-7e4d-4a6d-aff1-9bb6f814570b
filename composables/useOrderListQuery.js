import { useQuery } from "@tanstack/vue-query";

export const useOrderListQuery = (props = {}) => {
	const { network } = props
	const web3Store = useWeb3Store()

	return useQuery({
		queryKey: ["order", "list", network],
		queryFn: () => {
			console.log("query", network)
			return api.apiCall("GET", "/order", {
				network: network || web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol,
				take: 50,
			})
		},
		cacheTime: 0,
	})
}
