export const useMarketplaceToken = () => {
    const { $dayjs } = useNuxtApp();
    const tokenInfo = ref(null);
    const categories = ref([]);
    const visibleCategories = computed(() => {
        console.log('after slice', categories.value.slice(0, 3));
        return categories.value.slice(0, 3);
    });

    const hiddenCategories = computed(() => {
        console.log('after slice', categories.value.slice(0, 3));
        return categories.value.slice(3);
    });

    const investors = ref([]);
    const lots = ref([]);
    const route = useRoute();
    const isLoading = ref(true);
    const isTruncated = ref(false);
    const socialMedia = ref({
        telegram: null,
        twitter: null,
        discord: null,
        facebook: null,
        github: null
    })

    const twfChangesInfo = {
        false: {
          symbol: '-',
          color: 'text-error'
        },
        true: {
          symbol: '+',
          color: 'text-success'
        },
      }

    const tabSortMap = {
        bestDeal: "BEST_DEAL",
        unlockingSoon: "UNLOCK_SOON",
    };

    const activeTab = ref('bestDeal');

    function getCountDown(time) {
        const unlockStart = $dayjs.utc(time * 1);
        const diffInMs = unlockStart.diff($dayjs().utc());
        if (diffInMs <= 0) {
            return null;
        }
        const durationObj = $dayjs.duration(diffInMs);

        const days = Math.floor(durationObj.asDays()); // Total days
        const hours = Math.floor(durationObj.hours()); // Remaining hours after extracting days
        const minutes = durationObj.minutes().toString().padStart(2, "0");

        return `${days}d : ${hours}h : ${minutes}m`;
    }

    async function getTokenInfo() {
        try {
            const res = await api.apiCall('GET', '/marketplace/single-token-info', {
                network: route.query.network || web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol,
                ...(route.path.includes("/bid-only-token") ? {
                    token_ticker: route.params.token,
                } : {
                    token_address: route.params.token,
                })
            })

            console.log('token info api res', res.data.message);
            tokenInfo.value = res.data.message;

            tokenInfo.value.onchain_info = tokenInfo.value.onchain_info || {
                "categories": null,
                "websites": null,
                "description": "null",
                "repos": null,
                "chat_link": null,
                "telegram": null,
                "twitter": null
            }

            categories.value = tokenInfo.value.onchain_info.categories ? tokenInfo.value.onchain_info.categories.filter(item => !item.includes('Portfolio')) : [];
            investors.value = tokenInfo.value.onchain_info.categories ? tokenInfo.value.onchain_info.categories.filter(item => item.includes('Portfolio')) : [];



            mapSocialMedia(tokenInfo.value.onchain_info);

        } catch (error) {
            console.error(error);
        }
    }

    async function getTokenLots(sort_by, display_id) {
        try {
            const res = await api.apiCall('GET', '/marketplace/single-token-lot-info', {
                network: route.query.network || web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol,
                token_address: route.params.token,
                sort_by: sort_by,
                display_id: display_id
            });

            console.log('token lots res', res.data.message);

            lots.value = res.data.message.data;

        } catch (error) {
            console.error('token lots error', error);
        }
    }

    function checkIsTruncated() {
        // alert('run');
        let element = document.getElementById("tokenDesc");
        console.log(element);
        const fullHeight = element.scrollHeight;

        // Get the visible height of the content
        const visibleHeight = element.clientHeight;

        console.log('full height', fullHeight);
        console.log('visible height', visibleHeight);

        // Compare heights
        isTruncated.value = fullHeight > visibleHeight;
    }

    function mapSocialMedia(onchainInfo) {
        console.log('onchain info', onchainInfo);

        // Map values from onchainInfo
        socialMedia.value.discord = onchainInfo.chat_link?.[0] || '';
        socialMedia.value.telegram = onchainInfo.telegram || '';
        socialMedia.value.twitter = onchainInfo.twitter || '';
        socialMedia.value.github = onchainInfo.repos?.github?.[0] || '';

        // Filter out empty strings and rebuild the object
        socialMedia.value = Object.fromEntries(
            Object.entries(socialMedia.value).filter(([key, value]) => value !== '' && value != null)
        );
    }

    onMounted(async () => {
        await getTokenInfo();
        await getTokenLots('BEST_DEAL');

        isLoading.value = false;

        await nextTick();

        checkIsTruncated();
    });


    return {
        tokenInfo,
        isLoading,
        isTruncated,
        categories,
        investors,
        visibleCategories,
        hiddenCategories,
        socialMedia,
        lots,
        activeTab,
        twfChangesInfo,
        tabSortMap,
        getCountDown,
        getTokenLots
    }
}
