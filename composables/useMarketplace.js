import mocaBanner from "~/assets/images_new/marketplace/banners/moca_full_banner_1_5x.webp";
import memeBanner from "~/assets/images_new/marketplace/banners/meme_full_banner_1_5x.webp";
import scrollBanner from "~/assets/images_new/marketplace/banners/scroll_full_banner_1_5x.webp";
import mobileMocaBanner from "~/assets/images_new/marketplace/banners/moca_mobile_banner.webp";
import mobileMemeBanner from "~/assets/images_new/marketplace/banners/meme_mobile_banner.webp";
import mobileScrollBanner from "~/assets/images_new/marketplace/banners/scroll_mobile_banner.webp";

import solanaBanner from "~/assets/images_new/marketplace/banners/solana_3x.webp";
import avalancheBanner from "~/assets/images_new/marketplace/banners/avalanche_3x.webp";

// bid to win web banners
import bidToWinWebBanner from "~/assets/images_new/marketplace/banners/bid_to_win_web.png";
// bid to win mobile banners
import bidToWinMobileBanner from "~/assets/images_new/marketplace/banners/bid_to_win_mobile.png";

// prelaunch web banners
import solanaWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/solana_3x.webp";
import avalancheWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/avalanche_3x.webp";
import zksyncWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/zksync_3x.webp";
import ucoinWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/ucoin.png";
import sandboxWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/sandbox.png";

// prelaunch mobile banners
import solanaMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/solana_mobile_3x.webp";
import avalancheMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/avalanche_mobile_3x.webp";
import zksyncMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/zksync_mobile_3x.webp";
import ucoinMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/ucoin_mobile.png";
import sandboxMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/sandbox_mobile.png";

import { TIME_UNIT } from "~/utils/const";

export const useMarketplace = () => {
  const { $dayjs } = useNuxtApp();
  const timeUnit = TIME_UNIT;
  const route = useRoute()
  const web3Store = useWeb3Store()

  // const mobileBanners = ref([
  //   {
  //     image: mobileMocaBanner
  //   },
  //   {
  //     image: mobileMemeBanner
  //   },
  //   {
  //     image: mobileScrollBanner
  //   }
  // ]);

  // const webBanners = ref([
  //   {
  //     image: solanaBanner
  //   },
  //   {
  //     image: avalancheBanner
  //   },
  //   {
  //     image: mocaBanner
  //   },
  //   {
  //     image: memeBanner
  //   },
  //   {
  //     image: scrollBanner
  //   }
  // ]);

  const webBanners = ref([
    {
      image: bidToWinWebBanner,
      url: "https://medium.com/secondswap-io/set-your-price-and-get-discounted-locked-tokens-0204059ba1dc",
      url_target: "_blank",
    },
    {
      image: avalancheWebPrelaunchBanner,
      url: "/bid-only-token/AVAX/?network=AVAX",
    },
    {
      image: ucoinWebPrelaunchBanner,
      url: "/top-token/******************************************/?network=ETH"
    },
    {
      image: solanaWebPrelaunchBanner,
      url: "/bid-only-token/SOL/?network=SOL",
    },
    {
      image: zksyncWebPrelaunchBanner,
      url: "/bid-only-token/ZK/?network=ZK",
    },
    {
      image: sandboxWebPrelaunchBanner,
      url: "/bid-only-token/SAND/?network=ETH",
    },
  ]);

  const mobileBanners = ref([
    {
      image: bidToWinMobileBanner,
      url: "https://medium.com/secondswap-io/set-your-price-and-get-discounted-locked-tokens-0204059ba1dc",
      _target: "_blank"
    },
    {
      image: avalancheMobilePrelaunchBanner,
      url: "/bid-only-token/AVAX/?network=AVAX",
    },
    {
      image: ucoinMobilePrelaunchBanner,
      url: "/top-token/******************************************/?network=ETH"
    },
    {
      image: solanaMobilePrelaunchBanner,
      url: "/bid-only-token/SOL/?network=SOL",
    },
    {
      image: zksyncMobilePrelaunchBanner,
      url: "/bid-only-token/ZK/?network=ZK",
    },
    {
      image: sandboxMobilePrelaunchBanner,
      url: "/bid-only-token/SAND/?network=ETH",
    },
  ]);



  const isLoading = ref(true);
  const bestDeals = ref([]);
  const unlockSoon = ref([]);
  const topTokens = ref([]);
  const bidOnlyTokens = ref([]);
  const allLots = ref([]);
  const allLotsMeta = ref([]);

  const activeTab = ref('bestDeal');

  const twfChangesInfo = {
    false: {
      symbol: '-',
      color: 'text-error'
    },
    true: {
      symbol: '+',
      color: 'text-success'
    },
  }


  async function getBestDeals() {
    try {
      const res = await api.apiCall('GET', '/marketplace/best-deal', {
        order: 'DESC',
        take: 50,
        sortBy: 'BEST_DEAL'
      });
      console.log('best deals res', res);

      bestDeals.value = res.data.message.data;
      return bestDeals.value
    } catch (error) {
      console.error('best deals error', error)
    }
  }

  async function getUnlockSoon() {
    try {
      const res = await api.apiCall('GET', '/marketplace/best-deal', {
        order: 'DESC',
        take: 50,
        sortBy: 'UNLOCKING_SOON'
      });
      console.log('unlock soon res', res);

      unlockSoon.value = res.data.message.data;
      return unlockSoon.value
    } catch (error) {
      console.error('unlock soon error', error)
    }
  }

  async function getTopTokens() {
    try {
      const res = await api.apiCall('GET', '/marketplace/token-summary', {
        order: 'DESC',
        take: 50,
        sortBy: 'POPULAR'
      });
      console.log('top tokens res', res.data);

      topTokens.value = res.data.message.data;
      return topTokens.value
    } catch (error) {
      console.error('top tokens error', error)
    }
  }

  async function getBidOnlyTokens() {
    try {
      const res = await api.apiCall('GET', '/marketplace/token-summary', {
        bid_only: true,
        order: 'DESC',
        take: 50,
        sortBy: 'POPULAR'
      });
      console.log('top tokens res', res.data);

      bidOnlyTokens.value = res.data.message.data;
      return bidOnlyTokens.value
    } catch (error) {
      console.error('top tokens error', error)
    }
  }

  async function getAllLots(sort_by, order, token_name, page_number, network) {
    try {
      const res = await api.apiCall('GET', '/marketplace/all-lot', {
        ...(network && { network: network }),
        page: page_number,
        take: 10,
        sort_by: sort_by,
        order: order,
        token_name: token_name
      });
      console.log('top tokens res', res.data);

      allLots.value = res.data.message.lots.data;
      allLotsMeta.value = res.data.message.lots.meta;
      return allLots.value;
    } catch (error) {
      console.error('top tokens error', error)
    }
  }

  function getCountDown(time) {
    console.log('count down time', $dayjs.utc(time).format('YYYY-MM-DD HH:mm:ss'));
    const unlockStart = $dayjs.utc(time * 1);
    const now = $dayjs.utc(); // Use UTC time for current time
    const diffInMs = unlockStart.diff(now);
    if (diffInMs <= 0) {
      return null;
    }
    const durationObj = $dayjs.duration(diffInMs);

    const days = Math.floor(durationObj.asDays()); // Total days
    const hours = Math.floor(durationObj.hours()); // Remaining hours after extracting days
    const minutes = durationObj.minutes().toString().padStart(2, "0");

    return `${days}d : ${hours}h : ${minutes}m`;
  };

  async function refetch() {
    const [bestDeals, unlockSoon, topTokens, bidOnlyTokens, allLots] = await Promise.all([
      getBestDeals(),
      getUnlockSoon(),
      getTopTokens(),
      getBidOnlyTokens(),
      getAllLots(),
    ])
    return {
      bestDeals,
      unlockSoon,
      topTokens,
      bidOnlyTokens,
      allLots
    }
  }

  onMounted(async () => {
    console.log("mounted")
    await refetch()
    isLoading.value = false;
  });

  return {
    mobileBanners,
    webBanners,
    isLoading,
    bestDeals,
    unlockSoon,
    topTokens,
    bidOnlyTokens,
    allLots,
    allLotsMeta,
    twfChangesInfo,
    getCountDown,
    activeTab,
    getBestDeals,
    getAllLots,
    refetch,
  };
}
