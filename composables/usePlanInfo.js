export const usePlanInfo = (tokenAddress) => {
    const web3Store = useWeb3Store(); // Store for web3 related operations and state

    const isLoading = ref(true); // Loading state for data fetching
    const currentToken = ref(null); // Current token details including name, ticker, address etc
    const plans = ref([]); // Array of available plans/lots for the token
    const plan = ref(null); // Currently selected plan/lot
    const maxSupply = ref(0);


    async function getTokenPlans() {
        const res = await api.apiCall("GET", "/user/lot/details", {
            token_address: tokenAddress,
            has_lot_amount: "true",
            is_fully_claimed: "false"
            // has_market_amount : "true"
        });

        if (!currentToken.value) {
            currentToken.value = res.data.message.data;
        }

        plans.value = res.data.message.data.lots[route.params.plan_index];

        maxSupply.value = await web3Store.getMaxSupply(
            currentToken.value.token_address
        );

        useHead({
            title: `${currentToken.value.token_name} (${currentToken.value.token_ticker})`,
        });

    }

    onMounted(async () => {
        await getTokenPlans();
        isLoading.value = false;
    });

    return {
        isLoading,
        currentToken,
        plan
    }
}