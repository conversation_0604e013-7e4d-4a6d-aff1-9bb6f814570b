import { useSweetAlertStore } from "../stores/sweetAlert";
import { useWeb3Store } from "../stores/web3";
import { useChart } from "./useChart";
import { divideNumberUsingDecimals, toBigNumber } from "~/utils/number";

export const useTokenInfo = (tokenAddress) => {

  const web3Store = useWeb3Store();
  const sweetAlertStore = useSweetAlertStore();
  const route = useRoute()

  const isLoading = ref(true);
  const plans = ref([]);
  const txns = ref([]);
  const currentToken = ref(null);

  const maxSupply = ref(0);
  const tokenBalance = ref(0);
  const claimable = ref(0);
  const sellable = ref(0);

  const selectedPlan = ref(0);
  const selectedIndex = ref(0);

  const planType = ref('active');
  const planInfo = ref('schedule')

  const apiUrl = useRuntimeConfig().public.apiUrl;

  const planTypeTabStyle = {
    false: "",
    true: "bg-gradient-dark text-white",
  };

  const { generateChart } = useChart(selectedPlan, currentToken);

  async function getTokenPlans() {
    const res = await api.apiCall("GET", "/user/lot/details", {
      token_address: tokenAddress,
      has_lot_amount: "true",
      is_fully_claimed: planType.value != 'active',
      // has_market_amount : "true"
    });

    if (!currentToken.value) {
      currentToken.value = res.data.message.data;
    }

    plans.value = res.data.message.data.lots;

    maxSupply.value = await web3Store.getMaxSupply(
      currentToken.value.token_address
    );

    useRouteStore().$patch({
      previousUrl: "/myassets",
      previousName: "My Assets",
      isShowPrevious: true,
      name: `${currentToken.value.token_name} (${currentToken.value.token_ticker})`,
      isShowLink: true,
      tokenInfo: currentToken.value
    });

    useHead({
      title: `${currentToken.value.token_name} (${currentToken.value.token_ticker})`,
    });

  }

  async function getTokenBalance() {
    tokenBalance.value = await web3Store.getTokenBalance(tokenAddress);
  }

  async function getClaimable() {
    console.log("claimable address", selectedPlan.value.vesting_address);
    const claimableData = await web3Store.getClaimable(selectedPlan.value.vesting_address);

    claimable.value = divideNumberUsingDecimals(claimableData[0].toString(), selectedPlan.value.token_decimal).toString();
  }

  function getSellable() {

    const totalAllocated = divideNumberUsingDecimals(
      selectedPlan.value.total_allocated ?? 0,
      selectedPlan.value.token_decimal ?? 0
    ).toString();

    const marketAmount = divideNumberUsingDecimals(
      selectedPlan.value.market_amount ?? 0,
      selectedPlan.value.token_decimal ?? 0
    ).toString();

    const claimedAmount = divideNumberUsingDecimals(
      selectedPlan.value.claimed_amount ?? 0,
      selectedPlan.value.token_decimal ?? 0
    ).toString();

    const sellLimit = divideNumberUsingDecimals(
      selectedPlan.value.selling_limit ?? 0,
      selectedPlan.value.token_decimal ?? 0
    ).toString();

    const listedAmount = divideNumberUsingDecimals(
      selectedPlan.value.listed_amount ?? 0,
      selectedPlan.value.token_decimal ?? 0
    ).toString();

    const claimableAmount = toBigNumber(totalAllocated).plus(marketAmount).minus(claimedAmount).toString();

    console.log(
      "sellable amount",
      totalAllocated,
      marketAmount,
      claimedAmount,
      sellLimit,
      listedAmount,
      claimableAmount
    );

    sellable.value = claimableAmount > sellLimit * 1
      ? sellLimit * 1
      : claimableAmount;

    console.log('sellable ref', sellable.value);

    console.log({
      'claimable amount': claimableAmount,
      'sell limit': sellLimit,
      'listed amount': listedAmount,
      'result 1': sellLimit * 1 - listedAmount * 1,
      'result 2': claimableAmount,
    })

    return claimableAmount > sellLimit * 1
      ? sellLimit * 1 - listedAmount * 1
      : claimableAmount;
  }

  async function changePlanType(type) {

    // localStorage.removeItem('plan-type');

    localStorage.setItem('plan-type', type);

    isLoading.value = true;

    planType.value = type;

    // selectedPlan.value = plans.value[0];

    selectedIndex.value = 0;

    await init();

    isLoading.value = false;
  }

  async function claimToken(address) {
    useSweetAlertStore().showLoadingAlert("Processing", "claiming");

    try {
      const result = await web3Store.claimToken(address);

      if (result) {
        getClaimable();
        getTokenBalance();
        getTokenPlans();
      }

      useSweetAlertStore().showAlert("Success", "Claim Success", "success");

      // changePlanType('claimed');

    } catch (error) {
      useSweetAlertStore().showAlert("error", "Transaction Declined", "error");
    }
  }

  async function listSuccessUpdate() {

    useSweetAlertStore().showLoadingAlert('Updating', 'updating data');

    // await new Promise(resolve => setTimeout(resolve, 5000));

    await getTxns();
    await getTokenPlans();

    selectedPlan.value = plans.value[selectedIndex.value];

    await getClaimable();
    await getSellable();

    useSweetAlertStore().showAlert('Success', 'data updated', 'success');
  }

  async function init() {
    if (localStorage.getItem('plan-type')) {
      planType.value = localStorage.getItem('plan-type');
    }

    await getTokenPlans();

    if (localStorage.getItem('active-plan') && planType.value == 'active') {
      const activePlanObj = JSON.parse(localStorage.getItem('active-plan'));

      console.log('activePlanObj', activePlanObj);

      const activePlan = plans.value.findIndex(plan => plan.lot_id == activePlanObj.planId);

      selectedIndex.value = activePlan == -1 ? 0 : activePlan;
    }

    selectedPlan.value = plans.value[selectedIndex.value] ?? null;

    console.log('selected plan', selectedPlan.value);

    if (web3Store.isInit) {
      await getTokenBalance();

      if (selectedPlan.value) {
        await getClaimable();
        await getSellable();
        await getTxns();
      }

    }

    isLoading.value = false;

    await nextTick();

    setTimeout(() => {
      generateChart();
    }, 1000)

  }

  function showListModal(vesting_address) {
    document.getElementById("listModal").showModal();
  }

  async function changePlan(planIndex) {
    isLoading.value = true;
    selectedIndex.value = planIndex;

    localStorage.setItem('active-plan', JSON.stringify({
      'planId': plans.value[selectedIndex.value].lot_id,
      'planIndex': planIndex
    }));
    await init();
    isLoading.value = false;
  }


  async function changePlanInfo(tab) {
    planInfo.value = tab;

    if (tab == 'schedule') {
      await nextTick();
      generateChart();
    }
  }

  async function getTxns() {
    try {
      const res = await api.apiCall("GET", "/market-transaction/history", {
        plan_id: selectedPlan.value.vesting_id,
        // action: "USER_CLAIM",
        network: route.query.network || web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol,
        take: 50,
      });

      txns.value = res.data.message.data;

      console.log("txn history", res);
    } catch (error) {
      console.error(error);
    }
  }

  web3Store.$subscribe(async (mutation, state) => {
    if (state.isInit) {
      await getTokenPlans();
      getTokenBalance();
      getClaimable();
      getSellable();
      getTxns();
    }
  });

  onMounted(() => {
    init()
  });

  return {
    sellable,
    claimable,
    selectedPlan,
    web3Store,
    isLoading,
    plans,
    currentToken,
    tokenBalance,
    maxSupply,
    getTokenPlans,
    planTypeTabStyle,
    planType,
    changePlanType,
    getClaimable,
    showListModal,
    claimToken,
    planInfo,
    changePlanInfo,
    txns,
    changePlan,
    init,
    listSuccessUpdate
  }
}
