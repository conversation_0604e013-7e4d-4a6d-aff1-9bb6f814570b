<template>
	<div v-if="displayError" class="relative px-16 py-10 text-white">
		<img src="https://files-secondswap-demo.secondswaptest.net/secondswap-icon.png" alt="Icon" width="200px" />
		<img class="absolute top-0 left-0 w-[100vw] h-[100vh]" src="/public/assets/error-page-background.png" />
		<div class="flex flex-col items-center gap-8 py-20 md:gap-16 md:py-40">
			<h1 class="text-4xl font-bold md:text-9xl">{{ displayError.code }}</h1>
			<div class="flex flex-col items-center justify-center gap-4">
				<div class="text-xl font-semibold md:text-6xl">{{ displayError.title }}</div>
				<div class="text-center md:text-2xl">{{ displayError.message }}</div>
			</div>
		</div>
	</div>
</template>

<script setup>
const error = useError()
const displayError = ref(null)
onMounted(() => {
	if (error.value.statusCode === 404) {
		displayError.value = {
			code: 404,
			title: "PAGE NOT FOUND",
			message: "Oops! Looks like this page took a wrong turn and got lost in the internet wilderness!"
		}
	} else {
		displayError.value = {
			code: 500,
			title: "SOMETHING WENT WRONG",
			message: "Whoops something went wrong. Check back in again in a while.",
		}
	}
})
</script>
