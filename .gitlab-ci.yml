stages:
  - install
  - build
  - deploy

variables:
  NODE_VERSION: "18"

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/

install_dependencies:
  stage: install
  image: node:${NODE_VERSION}
  script:
    - npm ci
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 day

build_project:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - rm -rf .output/
    - npm run build
    - ls -la .output/
  artifacts:
    paths:
      - .output/
      - ecosystem.config.cjs
    expire_in: 1 week
  dependencies:
    - install_dependencies
  only:
    - dev

deploy_dev:
  stage: deploy
  image: node:${NODE_VERSION}
  before_script:
    - echo 'SSH Host is:' $SSH_HOST
    - apt-get update -qq && apt-get install -y -qq openssh-client
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    # Create timestamped deployment directory
    - TIMESTAMP=$(date +%Y%m%d%H%M%S)
    - DEPLOY_DIR="$DEPLOY_PATH/releases/$TIMESTAMP"
    
    # Create directory structure on server
    - ssh $SSH_USER@$SSH_HOST "mkdir -p $DEPLOY_DIR"
    
    # Copy necessary files to server
    - scp -r .output/ $SSH_USER@$SSH_HOST:$DEPLOY_DIR/.output/
    - scp ecosystem.config.cjs $SSH_USER@$SSH_HOST:$DEPLOY_DIR/
    
    # Install dependencies and build on server
    - ssh $SSH_USER@$SSH_HOST "cd $DEPLOY_DIR && npm ci && npm run build"
    
    # Create/update symlink to current release
    - ssh $SSH_USER@$SSH_HOST "ln -sfn $DEPLOY_DIR $DEPLOY_PATH/current"
    
    # PM2 process management with zero downtime
    - |
      ssh $SSH_USER@$SSH_HOST "cd $DEPLOY_PATH && \
      sudo pm2 describe $PM2_APP_NAME > /dev/null || sudo pm2 start ecosystem.config.cjs --name $PM2_APP_NAME && \
      sudo pm2 reload $PM2_APP_NAME --update-env && \
      sudo pm2 save"
    
    # Clean up old releases (keep last 5)
    - ssh $SSH_USER@$SSH_HOST "ls -dt $DEPLOY_PATH/releases/* | tail -n +6 | xargs rm -rf"
  dependencies:
    - build_project
  only:
    - dev
  environment:
    name: dev