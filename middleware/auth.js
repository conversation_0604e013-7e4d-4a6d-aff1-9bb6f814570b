export default defineNuxtRouteMiddleware((to, from) => {
    // const isWalletConnected = useState('isWalletConnected') // Using Pinia or a global state

    // if (!isWalletConnected.value) {
    //     return navigateTo('/connect-wallet') // Redirect to wallet connection page if not connected
    // }


    if (process.client) {
        const isLoggedIn = localStorage.getItem('isLoggedIn')

        // If the wallet is not connected, redirect the user
        if (!isLoggedIn) {
            document.getElementById('connectModal').checked = true;
            return navigateTo('/');
            // return navigateTo('/connect-wallet')
        }
    }

})
