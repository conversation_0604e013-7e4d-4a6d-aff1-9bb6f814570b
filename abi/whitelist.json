[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "whitelistContract", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_lotOwner", "type": "address"}], "name": "WhitelistCreated", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_max<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "address", "name": "_lotOwner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}]