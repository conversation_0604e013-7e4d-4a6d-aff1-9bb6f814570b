[{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "AddressInsufficientBalance", "type": "error"}, {"inputs": [], "name": "FailedInnerCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}], "name": "CoinAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "penaltyFee", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "seller", "type": "address"}], "name": "Delisted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}], "name": "Listed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}], "name": "LotFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}], "name": "LotUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "referral", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "buyerFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "sellerFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "referralReward", "type": "uint256"}], "name": "Purchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "vestingPlan", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "listingId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "whitelist<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "WhitelistCreated", "type": "event"}, {"inputs": [], "name": "BASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}], "name": "addCoin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "string", "name": "functionSignature", "type": "string"}], "name": "doesFunctionExist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingPlan", "type": "address"}, {"internalType": "uint256", "name": "_listingId", "type": "uint256"}], "name": "freezeLot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_marketplaceSetting", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isTokenSupport", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingPlan", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_price", "type": "uint256"}, {"internalType": "uint256", "name": "_discountPct", "type": "uint256"}, {"internalType": "enum SecondSwap_Marketplace.ListingType", "name": "_listingType", "type": "uint8"}, {"internalType": "enum SecondSwap_Marketplace.DiscountType", "name": "_discountType", "type": "uint8"}, {"internalType": "uint256", "name": "_max<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "address", "name": "_currency", "type": "address"}, {"internalType": "uint256", "name": "_minPurchaseAmt", "type": "uint256"}, {"internalType": "bool", "name": "_isPrivate", "type": "bool"}], "name": "listVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "listings", "outputs": [{"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "total", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerUnit", "type": "uint256"}, {"internalType": "enum SecondSwap_Marketplace.ListingType", "name": "listingType", "type": "uint8"}, {"internalType": "enum SecondSwap_Marketplace.DiscountType", "name": "discountType", "type": "uint8"}, {"internalType": "uint256", "name": "discountPct", "type": "uint256"}, {"internalType": "uint256", "name": "listTime", "type": "uint256"}, {"internalType": "address", "name": "whitelist", "type": "address"}, {"internalType": "uint256", "name": "minPurchaseAmt", "type": "uint256"}, {"internalType": "enum SecondSwap_Marketplace.Status", "name": "status", "type": "uint8"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "address", "name": "vestingPlan", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "marketplaceSetting", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "nextListingId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_marketplaceSetting", "type": "address"}], "name": "setMarketplaceSettingAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingPlan", "type": "address"}, {"internalType": "uint256", "name": "_listingId", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address", "name": "_referral", "type": "address"}], "name": "spotPurchase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingPlan", "type": "address"}, {"internalType": "uint256", "name": "_listingId", "type": "uint256"}], "name": "unfreezeLot", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingPlan", "type": "address"}, {"internalType": "uint256", "name": "_listingId", "type": "uint256"}], "name": "unlistVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}]