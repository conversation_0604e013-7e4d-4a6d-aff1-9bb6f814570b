<template>
  <div data-theme="oldtheme" class="font-medium">
    <!-- <NuxtLayout> -->
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup>
import { Buffer } from "buffer";
globalThis.Buffer = globalThis.Buffer || Buffer
const web3Store = useWeb3Store()
onMounted(() => {
  web3Store.getAccountInfo()
})
</script>

<style scoped></style>
