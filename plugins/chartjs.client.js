// plugins/chartjs.client.js
import { defineNuxtPlugin } from '#app';
import Chart from "chart.js/auto";

import { LineElement, PointElement, LineController, CategoryScale, LinearScale } from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';

// Register the required Chart.js components
Chart.register(LineElement, PointElement, LineController, CategoryScale, LinearScale, annotationPlugin);

export default defineNuxtPlugin((nuxtApp) => {
    // Chart.js can now be used throughout the app
    nuxtApp.provide('chart', Chart);
});
