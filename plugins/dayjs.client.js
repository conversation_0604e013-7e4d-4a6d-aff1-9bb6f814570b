import { defineNuxtPlugin } from '#app';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';  // If you need the duration plugin
import relativeTime from 'dayjs/plugin/relativeTime';  // If you need relative time support
import advancedFormat from "dayjs/plugin/advancedFormat";
import utc from "dayjs/plugin/utc";
import timezone from 'dayjs/plugin/timezone';  // Add timezone plugin

dayjs.extend(duration);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(advancedFormat);
dayjs.extend(utc);

const testTimestamp = 1737538500000;
console.log('Plugin UTC Test:', dayjs(testTimestamp).format('YYYY-MM-DD HH:mm:ss'));

export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.provide('dayjs', dayjs);
});