<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonLotDetails></SkeletonLotDetails>
    </div>
    <div v-else class="min-h-screen">
      <div
        class="relative flex justify-center transition-all duration-200 ease-in-out"
      >
        <transition name="fade">
          <div
            v-if="isCopied"
            class="absolute -top-5 flex justify-center items-center gap-2 bg-[#50F18712] text-[#50F187] text-2sm w-[155px] h-[44px] rounded-[8px]"
          >
            <inline-svg
              width="16"
              height="16"
              :src="checkCircleIcon"
            ></inline-svg>
            <h6>Link Copied</h6>
          </div>
        </transition>
      </div>
      <div class="hidden md:block">
        <div class="relative flex items-center justify-between mb-10">
          <nuxt-link to="/">
            <div class="flex items-center gap-1">
              <inline-svg :src="chevronLeftIcon"></inline-svg>
              <h5 class="text-sm">Back</h5>
            </div>
          </nuxt-link>
          <div>
            <button
              :data-clipboard-text="link"
              class="copy-link btn border-primary text-primary bg-transparent h-8 min-h-8 !rounded-[4px] font-medium hover:border-primary hover:opacity-80"
            >
              <inline-svg :src="shareIcon"></inline-svg>
              <h5>Share</h5>
            </button>
          </div>
        </div>
      </div>

      <div class="flex flex-col gap-5 mb-5 md:flex-row">
        <div class="flex-1 hidden w-1/2 md:block">
          <LotInfo
            :lot="lot"
            :currentToken="currentToken"
            :maxSupply="maxSupply"
            :getCountDown="getCountDown"
          ></LotInfo>
        </div>
        <div class="w-full md:hidden">
          <LotMobileInfo
            :lot="lot"
            :currentToken="currentToken"
            :maxSupply="maxSupply"
            :getCountDown="getCountDown"
          ></LotMobileInfo>
        </div>
        <div class="w-full md:w-1/2">
          <LotBuy
            @openBuySummary="handleBuySummary"
            ref="buyComponent"
            :lot="lot"
            :currentToken="currentToken"
            :usdtBalance="usdtBalance"
          ></LotBuy>
        </div>
      </div>
      <LotGraph :lot="lot" :currentToken="currentToken"></LotGraph>

      <LotHistory
        :marketplace_display_id="lot.marketplace_display_id"
        :token_address="lot.token_address"
        :getCountDown="getCountDown"
        :twfChangesInfo="twfChangesInfo"
      ></LotHistory>

      <ModalBuySummary
        :lot="lot"
        :buyForm="buyComponent?.buyForm"
        :isMarketplaceBuy="true"
        :tokenImage="currentToken.token_image"
        :tokenPrice="currentToken.price"
        ref="newBuySummaryModal"
      ></ModalBuySummary>
    </div>
  </transition>
</template>

<script setup>
import numeral from "numeral";
import ClipboardJS from "clipboard";
import InlineSvg from "vue-inline-svg";
import chevronLeftIcon from "~/assets/images_new/icons/chevron-left.svg";
import shareIcon from "~/assets/images_new/icons/share.svg";
import checkCircleIcon from "~/assets/images_new/icons/check-circle.svg";
definePageMeta({
  layout: "new-app",
});

const {
  banners,
  isLoading,
  bestDeals,
  unlockSoon,
  topTokens,
  allLots,
  twfChangesInfo,
  getCountDown,
  activeTab,
  getBestDeals,
  getAllLots,
} = useMarketplace();

const { $dayjs } = useNuxtApp();

const web3Store = useWeb3Store();
const route = useRoute();
const router = useRouter();
const usdtBalance = ref(0);
const isCopied = ref(false);
const link = ref("");

const currentToken = ref(null);
const lot = ref(null);
const maxSupply = ref(0);
const countdown = ref(null);
const lot_display_id = ref(null);
const buyComponent = ref(null);
const newBuySummaryModal = ref(null);

async function getLotDetails() {
  // console.log('plan', route.params.plan.split('-'))

  const planIds = route.params.lot.split("-");
  // lot_display_id.value = common.formatLotId(planIds[1])
  console.log("plan", route.params);

  try {
    const apiRes = await api.apiCall("GET", "/marketplace/lot", {
      order: "DESC",
      page: 1,
      take: 1,
      plan_id: planIds[0],
      listing_id: planIds[2],
      network: route.query.network,
    });

    console.log("marketplace lots", apiRes.data.message);

    currentToken.value = apiRes.data.message.token;
    lot.value = apiRes.data.message.lots.data[0];
    lot.value.token_address = currentToken.value.token_address;
    lot.value.token_ticker = currentToken.value.token_ticker;
    lot.value.token_decimal = currentToken.value.decimal; // Add this line
  } catch (error) {
    console.error(error);
  }
}

async function getWhiteListInfo() {
  isWhitelist.value = await web3Store.checkIsWhitelist(
    lot.value.whitelist_address
  );
  whitelistInfo.value = await web3Store.getWhitelistInfo(
    lot.value.whitelist_address
  );
}

async function whitelist() {
  useSweetAlertStore().showLoadingAlert(
    "Processing",
    "Whitelist process is ongoing..."
  );

  try {
    await web3Store.whitelistAddress(lot.value.whitelist_address);

    await getLotDetails();
    await getWhiteListInfo();

    useSweetAlertStore().showAlert("Success", "Join Success", "success");
  } catch (error) {
    useSweetAlertStore().showAlert("error", "Transaction Declined", "error");
  }
}

function handleBuySummary() {
  console.log("handleBuySummary executed");

  newBuySummaryModal.value.handleClick();
}

web3Store.$onAction(async (action) => {
  console.log("action", action);
  if (action.name == "getAccountInfo") {
    setTimeout(async () => {
      const config = web3Store.getConfigByCurrentNetwork()
      await web3Store
        .getTokenBalance(
          config.usdtToken, 
          config.network.nativeCurrency.symbol
        ).then((bal) => {
          usdtBalance.value = bal;
        });
    }, 1100);
  }
});

// search
const isSearchModalVisible = ref(false);
function openSearchModal() {
  isSearchModalVisible.value = false;
  searchTerm.value = null;
  isSearchModalVisible.value = true;
}

async function closeSearchModal() {
  isSearchModalVisible.value = false;
  searchLotModalRef.value.searchTerm = null;
}

onMounted(async () => {
  // check if token network is current connected network
  const tokenNetworkSymbol = route.query.network
  await web3Store.switchNetwork(tokenNetworkSymbol)

  await getLotDetails();
  link.value = window.location.href;

  //clipboard
  var clipboard = new ClipboardJS(".copy-link");
  clipboard.on("success", function (e) {
    isCopied.value = true;
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
    e.clearSelection();
  });

  clipboard.on("error", function (e) {
    console.error("Action:", e.action);
    console.error("Trigger:", e.trigger);
  });

  console.log("lot display id in mounted", lot.value.marketplace_display_id);
  console.log("ref buy component", buyComponent.value);

  // await getHistory();

  // var clipboard = new ClipboardJS(".copy-btn");

  // clipboard.on("success", function (e) {
  //   useSweetAlertStore().showAlert("success", `copied success`, "success");

  //   e.clearSelection();
  // });

  maxSupply.value = await web3Store.getMaxSupply(
    currentToken.value.token_address
  );

  // countdown.value = setInterval(() => {
  //   const time = updateCountDown();

  //   if (time <= 0) {
  //     clearInterval(countdown.value);
  //   }
  // }, 1000);

  // const planIds = route.params.lot.split("-");
  // useRouteStore().$patch({
  //   previousUrl: ` /marketplace`,
  //   previousName: "Marketplace",
  //   isShowPrevious: true,
  //   // previousUrl: `/marketplace/${currentToken.value.token_address}`,
  //   // name: `${currentToken.value.token_name} (${currentToken.value.token_ticker})`,
  //   name: `${currentToken.value.token_name} ${common.formatLotId(
  //     currentToken.value.token_ticker,
  //     planIds[1],
  //     lot.value.list_id
  //   )}`,
  // });

  if (lot.value.is_private && web3Store.isInit) {
    await getWhiteListInfo();
  }

  // if (lot.value.listing_type == 1) {
  //   buyForm.output = web3Store.formatNumber(lot.value.remaining_listing);
  // }

  // buyForm.platformFee = buyForm.input * lot.value.buyer_fee;

  if (web3Store.isInit) {
    const config = web3Store.getConfigByCurrentNetwork()
    await web3Store
      .getTokenBalance(config.usdtToken, config.network.nativeCurrency.symbol)
      .then((bal) => {
        usdtBalance.value = bal;
      });
  }
  isLoading.value = false;
});
</script>

<style></style>
