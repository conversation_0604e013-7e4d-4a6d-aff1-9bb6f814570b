<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonMyAssets></SkeletonMyAssets>
    </div>
    <div v-else class="min-h-screen">
      <!-- Header Section -->
      <div class="pb-6 font-medium border-b border-textbox-border md:py-8">
        <h5 class="mb-2 text-sm md:text-base text-input-icons">Net Worth</h5>
        <div class="flex flex-col gap-3 items-start lg:flex-row lg:items-end">
          <h1 class="text-[32px] md:text-5xl text-white font-satoshiBlack leading-[42px]">
            {{
              formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)).plus(toBigNumber(claimableAmountSum)))
            }}
            USD
          </h1>
          <div class="flex gap-[8px] items-end">
            <inline-svg :src="lockIcon" class="w-[18px] h-[18px] md:w-6 md:h-6"></inline-svg>
            <h5 class="text-input-icons text-2sm md:text-xl leading-[20px]">
              Locked Value:
              <span class="text-white">
                {{
                  formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)))
                }}
              </span>
            </h5>
          </div>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <div class="mt-8">
        <div class="flex gap-8 border-b border-textbox-border">
          <div v-for="tab in mainTabs" :key="tab.id" class="px-4 pb-3 cursor-pointer relative" :class="{
            'border-b-2 border-primary text-white font-medium': activeMainTab === tab.id,
            'text-input-icons hover:text-white/70 transition-colors': activeMainTab !== tab.id
          }" @click="activeMainTab = tab.id">
            <div class="flex gap-2 items-center">
              <inline-svg v-if="tab.icon" :src="tab.icon" class="w-5 h-5" />
              <span>{{ tab.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="mt-8">
        <!-- Overview Tab -->
        <div v-if="activeMainTab === 'overview'" class="space-y-8">
          <!-- Next Unlock Card -->
          <div class="bg-white/[6%] rounded-xl p-6">
            <div class="flex items-center gap-3 mb-4">
              <inline-svg :src="lockIcon" class="w-6 h-6 text-primary"></inline-svg>
              <h3 class="text-xl font-medium text-white">Next Unlock</h3>
            </div>
            <div class="text-white">
              <div class="text-2xl font-satoshiBlack mb-2">SANDBOX</div>
              <div class="text-input-icons">Get Notified →</div>
            </div>
          </div>

          <!-- Locked Assets Summary -->
          <div class="bg-white/[6%] rounded-xl p-6">
            <div class="flex items-center gap-3 mb-4">
              <inline-svg :src="lockIcon" class="w-6 h-6"></inline-svg>
              <h3 class="text-xl font-medium text-white">Locked Assets</h3>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-input-icons text-sm">Total Claimable</div>
                <div class="text-white text-xl font-medium">0.00</div>
              </div>
              <div>
                <div class="text-input-icons text-sm">Total Locked</div>
                <div class="text-white text-xl font-medium">0.00</div>
              </div>
            </div>
          </div>
        </div>

        <!-- My Assets Tab -->
        <div v-if="activeMainTab === 'assets'" class="space-y-6">
          <div class="flex justify-between items-center">
            <h2 class="text-xl md:text-[28px] font-satoshiBlack text-white">My Assets</h2>
            <div class="flex gap-2 items-center text-2sm text-input-icons">
              <input type="checkbox" class="checkbox checkbox-primary h-4 w-4 rounded-[2px]" disabled />
              <span>Hide Small Balance</span>
            </div>
          </div>

          <div class="flex items-center gap-[8px] mb-6">
            <inline-svg :src="lockIcon" class="w-6 h-6"></inline-svg>
            <h3 class="flex items-end gap-2 leading-[22px] text-white">
              <div class="text-2sm md:text-xl">Tokens Locked:</div>
              <div class="text-[18px] md:text-xl">
                {{
                  formatUSDT(toBigNumber(userSummary.net_worth).minus(toBigNumber(claimableAmountSum)).toString())
                }}
                USDT
              </div>
            </h3>
          </div>

          <!-- Network Tabs -->
          <div class="flex border-b border-textbox-border">
            <div v-for="network in networks" :key="network.id" class="px-4 pb-3 cursor-pointer" :class="{
              'border-b-2 border-primary text-white font-medium': currentNetwork === network.id,
              'text-input-icons hover:text-white/70 transition-colors': currentNetwork !== network.id
            }" @click="changeNetwork(network.id)">
              <div class="flex gap-2 items-center">
                <img :src="network.icon" class="w-5 h-5" />
                <span>{{ network.name }}</span>
              </div>
            </div>
          </div>

          <!-- Assets Table/Cards -->
          <div class="mt-6">
            <TableMyAssets class="hidden lg:table" @showClaimEvent="claimToken($event.plan.vesting_address)"
              @showListEvent="showListModal($event.plan)" @showSummaryEvent="showSummaryModal($event.plan)"
              :tokenPlans="tokenPlans" :claimableAmounts="claimableAmounts"></TableMyAssets>

            <div v-if="!tokenPlans || tokenPlans.length == 0" class="lg:hidden">
              <NoData :redirectButton="true" content="No Asset Found"></NoData>
            </div>
            <div v-for="(token, index) in tokenPlans" class="pb-4 lg:hidden">
              <CardAsset @claimEvent="claimToken($event.plan.vesting_address)"
                @listEvent="handleMobileListModal($event.plan)"
                @summaryEvent="handleMobilePlanDetailsModal($event.plan)" :token="token"
                :claimableAmounts="claimableAmounts" :index="index"></CardAsset>
            </div>
          </div>
        </div>

        <!-- Listing Tab -->
        <div v-if="activeMainTab === 'listing'" class="space-y-6">
          <h2 class="text-xl md:text-2xl font-medium text-white">Listings</h2>

          <div class="flex gap-8 border-b border-textbox-border">
            <div
              :class="{ 'relative cursor-pointer px-4 pb-3': true, 'tab-active border-b-2 border-primary text-white font-medium': listingTab === 'active', 'text-input-icons hover:text-white/70 transition-colors': listingTab !== 'active' }"
              @click="listingTab = 'active'">Active Listing</div>
            <div
              :class="{ 'relative cursor-pointer px-4 pb-3': true, 'tab-active border-b-2 border-primary text-white font-medium': listingTab === 'orders', 'text-input-icons hover:text-white/70 transition-colors': listingTab !== 'orders' }"
              @click="listingTab = 'orders'">Indications of interest</div>
          </div>

          <div v-if="listingTab === 'active'" class="mt-6">
            <div v-if="userListedLots.length > 0">
              <div class="hidden lg:block">
                <TableLot :lots="userListedLots" :getCountDown="getCountDown" :getAllLots="getUserListedLots"
                  :twfChangesInfo="twfChangesInfo" @showBuyModalEvent="delistConfirm($event.lot)"
                  noDataContent="No Active Listings" :myAssets="true"></TableLot>
              </div>
              <Pagination @callback="handlePaginateUpdate()" :totalItems="userListedLotsMeta?.itemCount ?? 0"
                ref="paginateRef" />
              <div v-for="(lot, index) in userListedLots" class="pb-4 lg:hidden">
                <CardAllLot @showBuyModalEvent="delistConfirm($event.lot || lot)" :tokenImg="lot.token_image"
                  :networkImg="lot.network_image" :networkSymbol="lot.network_symbol" :totalListing="lot.total_listing"
                  :remainingListing="lot.remaining_listing ?? 0" :tokenName="lot.token_name"
                  :tokenTicker="lot.token_ticker" :listId="lot.list_id" :listingType="lot.listing_type"
                  :tokenPrice="divideNumberUsingDecimals(lot.token_price ?? 0, USDT_DECIMALS).toString()"
                  :bestPrice="divideNumberUsingDecimals(lot.listed_price ?? 0, USDT_DECIMALS).toString()"
                  :maxDiscount="lot.discount_pct" :unlockStart="lot.start_date" :displayId="lot.display_id"
                  :twfHourChanges="`${twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                    }${lot.twf_hour_changes.replace('-', '')}%`"
                  :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color" :lotId="common.formatLotId(
                    lot.token_ticker,
                    lot.display_id,
                    lot.list_id
                  )
                    " :buttonText="'Delist'"></CardAllLot>
              </div>
            </div>
            <div v-else>
              <NoData content="No Active Listings" />
            </div>
          </div>

          <div v-if="listingTab === 'orders'" class="mt-6">
            <MyAssetsListingOffers />
          </div>
        </div>

        <!-- Activity Tab -->
        <div v-if="activeMainTab === 'activity'" class="space-y-6">
          <h2 class="text-xl md:text-2xl font-medium text-white">Activity</h2>

          <div v-if="mockActivityData.length > 0" class="space-y-4">
            <div v-for="activity in mockActivityData" :key="activity.id"
              class="bg-white/[6%] rounded-xl p-4 hover:bg-white/[8%] transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                  <div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <span class="text-primary text-sm">{{ activity.type.charAt(0) }}</span>
                  </div>
                  <div>
                    <div class="text-white font-medium">{{ activity.action }}</div>
                    <div class="text-input-icons text-sm">{{ activity.timestamp }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-white font-medium">{{ activity.amount }}</div>
                  <div class="text-input-icons text-sm">{{ activity.token }}</div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="text-center py-12">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-white/[6%] flex items-center justify-center">
              <span class="text-input-icons text-2xl">📊</span>
            </div>
            <h3 class="text-white text-lg font-medium mb-2">No Activity</h3>
            <p class="text-input-icons">Some text here for trading?</p>
            <button class="btn btn-primary mt-4">Go to Marketplace</button>
          </div>
        </div>
      </div>

      <ModalList ref="newListModal" :lotData="selectedPlan" :usdtBalance="usdtBalance"
        @openListSummary="handleListSummary"></ModalList>

      <ModalListSummary ref="listSummaryModal" :selectedPlan="selectedPlan" :listForm="newListModal?.listForm"
        :currentNetwork="currentNetwork" @callback="initMyAssets" @openListModal="handleBackToList">
      </ModalListSummary>

      <ModalPlanDetails ref="planDetailsModal" :selectedPlan="selectedPlan"></ModalPlanDetails>
    </div>
  </transition>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import lockIcon from "~/assets/images_new/icons/lock.svg";
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber } from "~/utils/number";

// Main tabs configuration
const mainTabs = [
  { id: 'overview', name: 'Overview' },
  { id: 'assets', name: 'My Assets' },
  { id: 'listing', name: 'Listing' },
  { id: 'activity', name: 'Activity' }
];

// Network configuration
const networks = [
  {
    id: 'ETH',
    name: 'Ethereum',
    icon: 'https://files-secondswap-staging.secondswaptest.net/eth_4x.webp',
  },
  {
    id: 'AVAX',
    name: 'Avalanche',
    icon: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
  },
  {
    id: "SOL",
    name: "Solana",
    icon: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
  }
];

// Mock activity data since APIs are not available
const mockActivityData = ref([
  {
    id: 1,
    type: 'claim',
    action: 'Claimed tokens',
    amount: '1,250.00',
    token: 'USDT',
    timestamp: '2 hours ago'
  },
  {
    id: 2,
    type: 'list',
    action: 'Listed for sale',
    amount: '500.00',
    token: 'ETH',
    timestamp: '1 day ago'
  },
  {
    id: 3,
    type: 'buy',
    action: 'Purchased tokens',
    amount: '2,000.00',
    token: 'AVAX',
    timestamp: '3 days ago'
  }
]);

const {
  getUserListedLots,
  initMyAssets,
  getCountDown,
  delistConfirm,
  claimToken,
  tokenPlans,
  claimableAmounts,
  claimableAmountSum,
  showListModal,
  showSummaryModal,
  selectedPlan,
  web3Store,
  isLoading,
  userListedLots,
  userListedLotsMeta,
  userSummary,
  twfChangesInfo,
  currentNetwork,
  changeNetwork,
} = useMyAssets();

// Reactive state
const activeMainTab = ref('overview');
const listingTab = ref('active');
const newListModal = ref(null);
const listSummaryModal = ref(null);
const planDetailsModal = ref(null);
const usdtBalance = ref(0);
const paginateRef = ref(null);

onMounted(async () => {
  // Initialize with current network
});

web3Store.$subscribe(async (_, state) => {
  console.log("web3 state", state.isInit);

  if (state.isInit) {
    const config = web3Store.getConfigByCurrentNetwork()
    usdtBalance.value = await web3Store.getTokenBalance(
      config.usdtToken,
      config.network.nativeCurrency.symbol
    );
  }
});

async function handlePaginateUpdate() {
  const res = await api.apiCall("GET", "/user/listing", {
    page: paginateRef.value.currentPage,
    status: 0,
    take: 1,
    sort_by: "LISTED_TIME",
    network: currentNetwork.value.toUpperCase()
  });
  userListedLots.value = res.data.message.data;
  console.log("user listed lots pagination", userListedLots.value);
}

function handleMobileListModal(plan) {
  newListModal.value.handleClick();
  showListModal(plan);
}

function handleBackToList() {
  newListModal.value.handleClick();
}

function handleMobilePlanDetailsModal(plan) {
  planDetailsModal.value.handleClick();
  showSummaryModal(plan);
}

const handleListSummary = () => {
  listSummaryModal.value.handleClick();
};

definePageMeta({
  layout: "new-app",
  middleware: "auth",
});
</script>

<style scoped></style>
