<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonMyAssets></SkeletonMyAssets>
    </div>
    <div v-else>
      <div class="pb-6 font-medium border-b border-textbox-border md:py-8">
        <h5 class="mb-2 text-sm md:text-base text-input-icons">Net Worth</h5>
        <div class="flex flex-col gap-3 items-start lg:flex-row lg:items-end">
          <h1 class="text-[32px] md:text-5xl text-white font-satoshiBlack leading-[42px]">
            {{
              formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)).plus(toBigNumber(claimableAmountSum)))
            }}
            USDT
          </h1>
          <div class="flex gap-[8px] items-end">
            <inline-svg :src="lockIcon" class="w-[18px] h-[18px] md:w-6 md:h-6"></inline-svg>
            <h5 class="text-input-icons text-2sm md:text-xl leading-[20px]">
              Locked Value:
              <span class="text-white">
                {{
                  formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)))
                }}
              </span>
            </h5>
          </div>
        </div>
      </div>

      <div class="mt-8 text-white md:mt-12">
        <div class="flex justify-between items-center mb-6 md:mb-6">
          <h1 class="text-xl md:text-[28px] font-satoshiBlack">My Assets</h1>
          <div class="md:hidden">
            <div class="flex gap-2 items-center text-2sm ml-3 h-[30px]">
              <input type="checkbox" class="checkbox checkbox-primary h-4 w-4 rounded-[2px]" disabled />
              <h5>Hide Small Balance</h5>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-[8px] mb-6 lg:mb-0">
          <inline-svg :src="lockIcon" class="w-6 h-6"></inline-svg>
          <h3 class="flex items-end gap-2 leading-[22px]">
            <div class="text-2sm md:text-xl">Tokens Locked:</div>
            <div class="text-[18px] md:text-xl">
              {{
                formatUSDT(toBigNumber(userSummary.net_worth).minus(toBigNumber(claimableAmountSum)).toString())
              }}
              USDT
            </div>
          </h3>
          <div class="hidden md:block">
            <div class="flex gap-2 items-center ml-3 text-2sm">
              <input type="checkbox" class="checkbox checkbox-primary h-4 w-4 rounded-[2px]" disabled />
              <h5>Hide Small Balance</h5>
            </div>
          </div>
        </div>

        <!-- Network Tabs -->
        <div class="flex my-6 border-b border-textbox-border">
          <div v-for="network in networks" :key="network.id" class="px-4 pb-3 cursor-pointer" :class="{
            'border-b-2 border-primary text-white font-medium': currentNetwork === network.id,
            'text-input-icons': currentNetwork !== network.id
          }" @click="changeNetwork(network.id)">
            <div class="flex gap-2 items-center">
              <!-- <inline-svg v-if="network.icon" :src="network.icon" class="w-5 h-5" /> -->
              <img :src="network.icon" class="w-5 h-5" />
              <span>{{ network.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="mb-10 md:mb-[72px]">
        <TableMyAssets class="hidden lg:table" @showClaimEvent="claimToken($event.plan.vesting_address)"
          @showListEvent="showListModal($event.plan)" @showSummaryEvent="showSummaryModal($event.plan)"
          :tokenPlans="tokenPlans" :claimableAmounts="claimableAmounts"></TableMyAssets>

        <div v-if="!tokenPlans || tokenPlans.length == 0" class="md:hidden">
          <NoData :redirectButton="true" content="No Asset Found"></NoData>
        </div>
        <div v-for="(token, index) in tokenPlans" class="pb-4 lg:hidden">
          <CardAsset @claimEvent="claimToken($event.plan.vesting_address)"
            @listEvent="handleMobileListModal($event.plan)" @summaryEvent="handleMobilePlanDetailsModal($event.plan)"
            :token="token" :claimableAmounts="claimableAmounts" :index="index"></CardAsset>
        </div>
      </div>
      <div>
        <h1 class="pb-6 text-xl font-medium text-white md:text-2xl md:pb-8">
          Listings
        </h1>
        <div>
          <div class="flex gap-8 pb-8 md:gap-10 text-2sm lg:border-b border-textbox-border">
            <div
              :class="{ 'relative cursor-pointer': true, 'tab-active': listingTab === 'active', 'text-white/50': listingTab !== 'active' }"
              @click="listingTab = 'active'">Active Listing</div>
            <div
              :class="{ 'relative cursor-pointer': true, 'tab-active': listingTab === 'orders', 'text-white/50': listingTab !== 'orders' }"
              @click="listingTab = 'orders'">Indications of interest</div>
          </div>

          <div v-if="listingTab === 'active'">
            <div v-if="userListedLots.length > 0">
              <div class="hidden mt-2 lg:block">
                <TableLot :lots="userListedLots" :getCountDown="getCountDown" :getAllLots="getUserListedLots"
                  :twfChangesInfo="twfChangesInfo" @showBuyModalEvent="delistConfirm($event.lot)"
                  noDataContent="No Active Listings" :myAssets="true"></TableLot>
              </div>
              <Pagination @callback="handlePaginateUpdate()" :totalItems="userListedLotsMeta?.itemCount ?? 0"
                ref="paginateRef" />
              <div v-for="(lot, index) in userListedLots" class="pb-4 lg:hidden">
                <CardAllLot @showBuyModalEvent="delistConfirm($event.lot || lot)" :tokenImg="lot.token_image"
                  :networkImg="lot.network_image" :networkSymbol="lot.network_symbol" :totalListing="lot.total_listing"
                  :remainingListing="lot.remaining_listing ?? 0" :tokenName="lot.token_name"
                  :tokenTicker="lot.token_ticker" :listId="lot.list_id" :listingType="lot.listing_type"
                  :tokenPrice="divideNumberUsingDecimals(lot.token_price ?? 0, USDT_DECIMALS).toString()"
                  :bestPrice="divideNumberUsingDecimals(lot.listed_price ?? 0, USDT_DECIMALS).toString()"
                  :maxDiscount="lot.discount_pct" :unlockStart="lot.start_date" :displayId="lot.display_id"
                  :twfHourChanges="`${twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                    }${lot.twf_hour_changes.replace('-', '')}%`"
                  :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color" :lotId="common.formatLotId(
                    lot.token_ticker,
                    lot.display_id,
                    lot.list_id
                  )
                    " :buttonText="'Delist'"></CardAllLot>
              </div>
            </div>
            <div v-else>
              <NoData content="No Active Listings" />
            </div>
          </div>

          <div v-if="listingTab === 'orders'">
            <MyAssetsListingOffers />
          </div>
        </div>
      </div>

      <ModalList ref="newListModal" :lotData="selectedPlan" :usdtBalance="usdtBalance"
        @openListSummary="handleListSummary"></ModalList>

      <ModalListSummary ref="listSummaryModal" :selectedPlan="selectedPlan" :listForm="newListModal?.listForm"
        :currentNetwork="currentNetwork" @callback="initMyAssets" @openListModal="handleBackToList">
      </ModalListSummary>

      <ModalPlanDetails ref="planDetailsModal" :selectedPlan="selectedPlan"></ModalPlanDetails>
    </div>
  </transition>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import sortIcon from "~/assets/images_new/icons/sort.svg";
import lockIcon from "~/assets/images_new/icons/lock.svg";
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT } from "~/utils/number";

// Network configuration
const networks = [
  {
    id: 'ETH',
    name: 'Ethereum',
    icon: 'https://files-secondswap-staging.secondswaptest.net/eth_4x.webp',
  },
  {
    id: 'AVAX',
    name: 'Avalanche',
    icon: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
  },
  {
    id: "SOL",
    name: "Solana",
    icon: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
  }
];

const {
  getActivePlans,
  getUserListedLots,
  initMyAssets,
  getUserSummary,
  getCountDown,
  delistConfirm,
  claimToken,
  tokenPlans,
  claimableAmounts,
  claimableAmountSum,
  showListModal,
  showSummaryModal,
  selectedPlan,
  getSellable,
  getVestingProgress,
  web3Store,
  isHalfProgressBg,
  isLoading,
  activePlans,
  claimedPlans,
  userListedLots,
  userListedLotsMeta,
  userSummary,
  toPlanInfo,
  twfChangesInfo,
  currentNetwork,
  changeNetwork,
} = useMyAssets();

const lots = ref(null);
const newListModal = ref(null);
const listSummaryModal = ref(null);
const planDetailsModal = ref(null);
const usdtBalance = ref(0);
const paginateRef = ref(null);
const listingTab = ref('active')

onMounted(async () => {
  // Initialize with current network
});

web3Store.$subscribe(async (mutation, state) => {
  console.log("web3 state", state.isInit);

  if (state.isInit) {
    const config = web3Store.getConfigByCurrentNetwork()
    usdtBalance.value = await web3Store.getTokenBalance(
      config.usdtToken,
      config.network.nativeCurrency.symbol
    );
  }
});

async function handlePaginateUpdate() {
  const res = await api.apiCall("GET", "/user/listing", {
    page: paginateRef.value.currentPage,
    status: 0,
    take: 1,
    sort_by: "LISTED_TIME",
    network: currentNetwork.value.toUpperCase()
  });
  userListedLots.value = res.data.message.data;
  console.log("user listed lots pagination", userListedLots.value);
}

function handleMobileListModal(plan) {
  newListModal.value.handleClick();
  showListModal(plan);
}

function handleBackToList() {
  newListModal.value.handleClick();
}

function handleMobilePlanDetailsModal(plan) {
  planDetailsModal.value.handleClick();
  showSummaryModal(plan);
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleListSummary = () => {
  listSummaryModal.value.handleClick();
};

definePageMeta({
  layout: "new-app",
  middleware: "auth",
});
</script>

<style scoped></style>
