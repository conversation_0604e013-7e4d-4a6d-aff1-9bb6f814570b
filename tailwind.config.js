/** @type {import('tailwindcss').Config} */
const { fontFamily: defaultTheme } = require('tailwindcss/defaultTheme')

export default {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./app.vue",
    "./error.vue",
    // './node_modules/preline/preline.js',
  ],
  theme: {
    extend: {
      screens: {
        'xs': '480px',
        'sm': '570px',
        'md': '768px',
        'lg': '1024px',
        '2lg': '1177px',
        'xl': '1290px',
        '2xl': '1536px',
        '3xl': '1620px'
      },
      // fontFamily: {
      //   sans: ['Inter', ...defaultTheme.sans],
      //   satoshiBlack: ['<PERSON><PERSON> Black', 'sans-serif'],
      //   satoshiBold: ['Satoshi Bold', 'sans-serif'],
      //   satoshiMedium: ['Satoshi Medium', 'sans-serif'],
      //   satoshiRegular: ['Satoshi Regular', 'sans-serif'],
      //   satoshiLight: ['Satoshi Light', 'sans-serif']
      // },
      fontFamily: {
        sans: ['Satoshi', ...defaultTheme.sans],
        satoshiBlack: ['Satoshi Black', 'sans-serif'],
        satoshiBold: ['Satoshi Bold', 'sans-serif'],
        satoshiMedium: ['Satoshi Medium', 'sans-serif'],
        satoshiRegular: ['Satoshi Regular', 'sans-serif'],
        satoshiLight: ['Satoshi Light', 'sans-serif']
      },
      fontWeight: {
        normal: 400,
        medium: 500,  // Changed from object to direct weight
        bold: 700,    // Changed from object to direct weight
        black: 900,   // Changed from object to direct weight
      },
      fontSize: {
        xs: '0.625rem',
        sm: '0.75rem',
        '2sm': '0.875rem',
        base: '1rem',
        xl: '1.25rem',
        '1.375xl':'1.375rem',   
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '3.5xl': '2rem',
        '4xl': '2.1875rem',
        '4.375xl': '2.375rem',
        '4.5xl': '2.5rem',
        '5xl': '3rem',
        '6xl': '4rem',
        '6.25xl': '4.25rem',
        '7xl': '4.375rem',
        '7.5xl': '5rem',
        '8xl': '6.25rem'
      },
      borderRadius: {
        '2sm': '0.25rem'
      },
      colors: {
        'input': '#383F43',
        'card': '#0E1526',
        'solid': '#123B4E',
        'sidebar': '#0B111E',
        'sidebar-active': '#1e232f',
        'error-dark': '#4E2121',
        'input-icons': '#84858E',
        'modal': '#181B2B',
        'modal-textbox': '#131627',
        'textbox-border': '#343849',
        'range-blue': '#0C4CAC',
        'range-grey': '#2B2D3D',
        'grey-radial': '#535561',
      },
    },

  },
  plugins: [
    require('daisyui'),
    // require('preline/plugin'),
  ],


  daisyui: {
    themes: [
      {
        newtheme: {

          "primary": "#7CD3F8",

          "secondary": "#191b2c",

          "accent": "#0C0F25",

          "neutral": "#1D2027",

          "base-100": "#090C1E",

          "info": "#2b2d3c",

          "success": "#50F187",

          "warning": "#ebb971",

          "error": "#FF6767",
        },
      },
    ],
  },
}

