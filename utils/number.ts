import BigNumber from "bignumber.js"
import { formatUnits, parseUnits } from "ethers"
import numeral from "numeral"
import { TOKEN_PRICE_FORMAT, USDT_PRICE_FORMAT } from "./const"

export const toBigNumber = (value: any): BigNumber => {
    return new BigNumber(value)
}

export const divideNumberUsingDecimals = (value: any, decimals: string | number): BigNumber => {
    const safeValue = toBigNumber(value).toFixed().toString()
    if (safeValue === "NaN") {
        return toBigNumber(0)
    }
    return toBigNumber(formatUnits(safeValue, Number(decimals)))
}

export const multiplyNumberUsingDecimals = (value: any, decimals: number): BigNumber => {
    return toBigNumber(parseUnits(value, decimals))
}

export const formatSmallNumber = (number: any, maxDecimals = 4) => {
    // Handle exponential notation and convert to string
    let cleanNumber = typeof number === 'string' ?
        number.replace(/,/g, '') :
        String(number);

    // Convert exponential notation to decimal
    if (cleanNumber.includes('e')) {
        cleanNumber = Number(cleanNumber).toFixed(20);
    }

    // If the original input had commas, preserve the format
    if (typeof number === 'string' && number.includes(',')) {
        return number;
    }

    const parts = cleanNumber.split('.');
    const hasDecimal = parts.length > 1;

    // If no decimal or within max decimal places, return original
    if (!hasDecimal || parts[1].length <= maxDecimals) {
        return cleanNumber;
    }

    // Count leading zeros after decimal
    let leadingZeros = 0;
    const decimals = parts[1];
    for (let i = 0; i < decimals.length; i++) {
        if (decimals[i] === '0') {
            leadingZeros++;
        } else {
            break;
        }
    }

    // Get the remaining significant digits and remove trailing zeros
    const remainingDigits = decimals.slice(leadingZeros).replace(/0+$/, '');

    // Convert to subscript without using external libraries
    const subscript = String(leadingZeros).split('').map(digit => {
        const subscripts = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉'];
        return subscripts[parseInt(digit)];
    }).join('');

    return `0.0${subscript}${remainingDigits}`;
}

export const formatUSDT = (value: BigNumber | number): string => {
    const valueBigNumber = toBigNumber(value)
    if (valueBigNumber.isLessThan(0.0001)) {
        return formatSmallNumber(valueBigNumber.toString())
    } else {
        return numeral(valueBigNumber.toString()).format(USDT_PRICE_FORMAT, Math.floor)
    }
}

export const formatToken = (value: BigNumber | number): string => {
    return numeral(toBigNumber(value).toString()).format(TOKEN_PRICE_FORMAT, Math.floor)
}
