import axios from "axios";
import { useWeb3Store } from "../stores/web3";

export default {
  async apiCall(method, endpoint, params = {}, isFormData = false) {

    const apiUrl = useRuntimeConfig().public.apiUrl;

    // console.log(baseUrl)
    let config = {};
    let errorMsg = "";

    if (method == "GET") {
      config = {
        withCredentials: true,
        params: params,
        headers: {
          // 'TRON-PRO-API-KEY':import.meta.env.VITE_API_KEY
        }
      };
    } else {
      config = {
        withCredentials: true,
        headers: {
          'Content-type': isFormData ? 'multipart/form-data' : 'application/json'
        }
      };
    }

    console.log(config);
    try {
      let response;
      if (method == "GET") {
        const api = await axios.get(apiUrl + endpoint, config);
        response = api;
      } else {

        if (isFormData) {
          console.log('isFormData')
          const formData = new FormData;

          Object.entries(params).forEach(([key, value]) => {
            console.log(`${key}: ${value}`);

            formData.append(key, value == null ? '' : value);
          });

          params = formData;
        }

        let api
        switch(method.toUpperCase()) {
          case "DELETE":
            api = await axios.delete(apiUrl + endpoint, config);
            break;
          default:
            api = await axios.post(apiUrl + endpoint, params, config);
        }

        response = api;
      }


      return response;

    } catch (error) {
      errorMsg = error.response;
      // console.log(error);
      if (error.response.status == 401) {
        // window.location = '/'   
        useWeb3Store().disconnect();    
        useNewWeb3Store().disconnect();
        navigateTo('/')
      }

      console.log(error.response);

      // if (error.response.status == 401) {
      //   // alert('need redirect')
      //   // this.$router.push('/login');
      //   // return;

      //   localStorage.clear();

      //   console.log(error);


      // }


      throw error;
    }
  },
};
